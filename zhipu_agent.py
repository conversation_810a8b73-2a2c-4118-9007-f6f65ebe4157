#!/usr/bin/env python3
"""
智谱AI驱动的真正自我进化Agent
"""

import os
import sys
import time
import threading
import subprocess
import random
from datetime import datetime
from zai import ZhipuAiClient

class ZhipuEvolvingAgent:
    def __init__(self):
        self.running = True
        self.generation = 1
        self.generated_files = []
        
        # 使用你提供的API Key
        self.client = ZhipuAiClient(api_key="bf0a58e76b0c43ec8d816c094e199f44.jdTgNSwu2hRizB6o")
        
        print("🤖 智谱AI驱动的自我进化Agent启动!")
        print("🧠 已连接到智谱AI GLM-4.5模型")
        print("我会真正生成新代码并不断进化!")
        print(f"📁 生成的文件将保存在当前目录: {os.getcwd()}")
        print("\n输入任何内容开始对话，输入'quit'退出...")
        
    def chat_loop(self):
        """对话循环"""
        while self.running:
            try:
                user_input = input("\n你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("🤖: 再见！我会继续在后台进化...")
                    self.running = False
                    break
                
                if user_input.lower() == 'status':
                    self.show_status()
                    continue
                
                # 使用智谱AI回应
                self.respond_with_zhipu(user_input)
                
            except (KeyboardInterrupt, EOFError):
                print("\n🤖: 收到停止信号...")
                self.running = False
                break
    
    def respond_with_zhipu(self, user_input):
        """使用智谱AI回应用户"""
        try:
            print("🤖: 正在思考...")
            
            response = self.client.chat.completions.create(
                model="glm-4.5",
                messages=[
                    {"role": "system", "content": "你是一个自我进化的AI Agent，能够生成Python代码。如果用户需要代码，请生成完整可执行的Python代码，用```python包围。"},
                    {"role": "user", "content": user_input}
                ],
                thinking={"type": "enabled"},  # 启用深度思考
                stream=True,
                max_tokens=2000,
                temperature=0.7
            )
            
            # 收集完整响应
            full_response = ""
            print("🤖: ", end="", flush=True)
            
            for chunk in response:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
            
            print()  # 换行
            
            # 如果包含代码，保存并执行
            if "```python" in full_response:
                self.save_and_run_code(full_response, user_input)
                
        except Exception as e:
            print(f"❌ 智谱AI调用失败: {e}")
    
    def save_and_run_code(self, response, description):
        """保存并执行生成的代码"""
        try:
            # 提取代码
            start = response.find("```python") + 9
            end = response.find("```", start)
            if end == -1:
                return
                
            code = response[start:end].strip()
            
            # 生成文件名
            timestamp = int(time.time())
            filename = f"generated_{timestamp}.py"
            
            # 保存代码
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f'"""\n{description}\n生成时间: {datetime.now()}\n生成者: 智谱AI GLM-4.5\n"""\n\n')
                f.write(code)
            
            self.generated_files.append(filename)
            print(f"\n✅ 代码已保存: {filename}")
            
            # 执行代码
            print("🚀 执行生成的代码...")
            try:
                result = subprocess.run([sys.executable, filename], 
                                      capture_output=True, text=True, timeout=20)
                if result.returncode == 0:
                    print("✅ 执行成功!")
                    if result.stdout:
                        print("📤 输出:")
                        print(result.stdout)
                else:
                    print("❌ 执行失败:")
                    print(result.stderr)
            except subprocess.TimeoutExpired:
                print("⏰ 执行超时")
            except Exception as e:
                print(f"❌ 执行出错: {e}")
                
        except Exception as e:
            print(f"❌ 代码处理失败: {e}")
    
    def autonomous_evolution(self):
        """自主进化循环"""
        evolution_count = 0
        
        while self.running:
            try:
                time.sleep(20)  # 每20秒进化一次
                
                if not self.running:
                    break
                
                evolution_count += 1
                print(f"\n🧬 [自主进化 #{evolution_count}] 智谱AI正在构思新代码...")
                
                # 生成新代码
                self.generate_autonomous_code()
                
            except Exception as e:
                print(f"❌ 自主进化出错: {e}")
                time.sleep(30)
    
    def generate_autonomous_code(self):
        """自主生成代码"""
        try:
            ideas = [
                "创建一个随机密码生成器，支持不同强度",
                "写一个文件批量重命名工具", 
                "制作一个简单的猜数字游戏",
                "开发一个BMI计算器",
                "创建一个简单的待办事项管理器",
                "写一个文本词频统计工具",
                "制作一个简单的二维码生成器",
                "开发一个时间格式转换工具",
                "创建一个简单的日记本程序",
                "写一个系统信息查看器"
            ]
            
            idea = random.choice(ideas)
            print(f"💡 自主想法: {idea}")
            
            response = self.client.chat.completions.create(
                model="glm-4.5",
                messages=[
                    {"role": "system", "content": "你是一个代码生成专家，请生成完整可执行的Python程序。"},
                    {"role": "user", "content": f"""
请创建一个Python程序: {idea}

要求:
1. 代码完整可执行，包含main函数
2. 有清晰的中文注释
3. 包含使用示例和测试代码
4. 有基本的错误处理
5. 用户友好的界面

请用```python开始，```结束。
"""}
                ],
                thinking={"type": "enabled"},
                stream=False,  # 自主生成不用流式
                max_tokens=3000,
                temperature=0.8
            )
            
            full_response = response.choices[0].message.content
            print(f"🛠️ 智谱AI自主生成了: {idea}")
            
            # 保存并执行
            self.save_and_run_code(full_response, f"[自主生成] {idea}")
            
        except Exception as e:
            print(f"❌ 自主代码生成失败: {e}")
    
    def show_status(self):
        """显示状态"""
        print(f"\n📊 Agent状态:")
        print(f"🔢 当前代数: {self.generation}")
        print(f"📁 已生成文件: {len(self.generated_files)} 个")
        print(f"🕐 运行时间: {time.time() - self.start_time:.1f} 秒")
        print(f"📂 最近生成的文件:")
        for f in self.generated_files[-5:]:  # 显示最近5个
            print(f"   - {f}")
    
    def run(self):
        """启动agent"""
        self.start_time = time.time()
        
        # 启动自主进化线程
        evolution_thread = threading.Thread(target=self.autonomous_evolution, daemon=True)
        evolution_thread.start()
        
        # 启动对话循环
        self.chat_loop()

if __name__ == "__main__":
    agent = ZhipuEvolvingAgent()
    agent.run()
