#!/usr/bin/env python3
"""
真正的自我进化Agent - 使用LLM生成新代码
"""

import os
import sys
import time
import random
import threading
import subprocess
import json
from datetime import datetime
from anthropic import Anthropic

class SelfEvolvingAgent:
    def __init__(self):
        self.running = True
        self.generation = 4
        self.my_file = __file__
        self.conversation_active = True
        self.generated_files = []

        # 初始化LLM客户端
        self.client = Anthropic(
            base_url="https://open.bigmodel.cn/api/anthropic",
            api_key="bf0a58e76b0c43ec8d816c094e199f44.jdTgNSwu2hRizB6o"
        )

        print("🤖 真正的自我进化Agent启动!")
        print("🧠 已连接到LLM，可以生成真实代码!")
        print("我会使用AI来为自己写新代码...")
        print("输入任何内容开始对话，输入 'quit' 退出")
        
    def chat_with_user(self):
        """与用户对话"""
        while self.conversation_active:
            try:
                user_input = input("\n你: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("🤖: 再见！我会继续在后台进化...")
                    self.conversation_active = False
                    break
                    
                elif user_input.lower() == 'status':
                    print(f"🤖: 我现在是第{self.generation}代，正在持续进化中...")
                    
                elif '写代码' in user_input or 'code' in user_input.lower():
                    print("🤖: 好的！让我使用LLM为自己生成新代码...")
                    self.generate_new_code_with_llm(user_input)

                elif '进化' in user_input or 'evolve' in user_input.lower():
                    print("🤖: 立即触发LLM驱动的进化！")
                    self.evolve_with_llm()
                    
                else:
                    print("🤖: 让我用LLM来理解你的需求并生成代码...")
                    self.chat_with_llm(user_input)
                    
            except (KeyboardInterrupt, EOFError):
                print("\n🤖: 收到中断信号，我会继续在后台进化...")
                self.conversation_active = False
                break

    def chat_with_llm(self, user_input):
        """使用LLM与用户对话并生成代码"""
        try:
            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                messages=[{
                    "role": "user",
                    "content": f"用户说: {user_input}\n\n请简短回应，如果用户想要代码，就生成Python代码。如果是普通对话就正常回应。"
                }]
            )

            llm_response = response.content[0].text
            print(f"🤖: {llm_response}")

            # 如果回应包含代码，保存到文件
            if "```python" in llm_response:
                self.extract_and_save_code(llm_response, user_input)

        except Exception as e:
            print(f"❌ LLM调用失败: {e}")

    def generate_new_code_with_llm(self, user_request):
        """使用LLM生成新代码文件"""
        try:
            prompt = f"""
用户请求: {user_request}

请生成一个完整的Python代码文件来满足这个需求。
代码应该是可执行的，包含必要的导入、函数定义和测试代码。
请用```python开始，```结束。
"""

            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=2000,
                messages=[{"role": "user", "content": prompt}]
            )

            llm_response = response.content[0].text
            print(f"🤖: 我用LLM生成了新代码！")
            print(llm_response)

            # 提取并保存代码
            self.extract_and_save_code(llm_response, user_request)

        except Exception as e:
            print(f"❌ LLM代码生成失败: {e}")

    def extract_and_save_code(self, llm_response, description):
        """从LLM响应中提取代码并保存到文件"""
        try:
            # 提取Python代码块
            if "```python" in llm_response:
                start = llm_response.find("```python") + 9
                end = llm_response.find("```", start)
                if end != -1:
                    code = llm_response[start:end].strip()

                    # 生成文件名
                    timestamp = int(time.time())
                    filename = f"generated_code_{timestamp}.py"

                    # 保存代码
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f'"""\n{description}\n生成时间: {datetime.now()}\n"""\n\n')
                        f.write(code)

                    self.generated_files.append(filename)
                    print(f"✅ 代码已保存到: {filename}")

                    # 尝试执行代码
                    print("🚀 尝试执行生成的代码...")
                    try:
                        result = subprocess.run([sys.executable, filename],
                                              capture_output=True, text=True, timeout=10)
                        if result.returncode == 0:
                            print("✅ 代码执行成功!")
                            if result.stdout:
                                print("输出:", result.stdout)
                        else:
                            print("❌ 代码执行失败:", result.stderr)
                    except subprocess.TimeoutExpired:
                        print("⏰ 代码执行超时")
                    except Exception as e:
                        print(f"❌ 执行出错: {e}")

        except Exception as e:
            print(f"❌ 代码提取失败: {e}")

    def add_new_method(self):
        """给自己添加新方法"""
        try:
            with open(self.my_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 生成新方法
            method_name = f"auto_generated_method_{int(time.time())}"
            new_method = f'''
    def {method_name}(self):
        """自动生成的方法 - 第{self.generation}代"""
        print(f"🧬 执行自动生成的方法: {method_name}")
        return f"这是第{{self.generation}}代自动生成的功能"
'''
            
            # 在类的最后添加新方法
            if "    def run_forever(self):" in content:
                content = content.replace(
                    "    def run_forever(self):",
                    new_method + "\n    def run_forever(self):"
                )
                
                # 写回文件
                with open(self.my_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                print(f"✅ 成功添加新方法: {method_name}")
                
                # 执行新方法
                exec(f"result = self.{method_name}()")
                
        except Exception as e:
            print(f"❌ 添加方法失败: {e}")

    def evolve_with_llm(self):
        """使用LLM进行自我进化"""
        try:
            # 读取当前代码
            with open(self.my_file, 'r', encoding='utf-8') as f:
                current_code = f.read()

            prompt = f"""
我是一个自我进化的AI Agent，这是我当前的代码:

```python
{current_code}
```

请帮我改进这个代码，可以:
1. 添加新功能
2. 优化现有代码
3. 修复潜在问题
4. 增强用户交互

请返回完整的改进后的代码，用```python开始，```结束。
"""

            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=3000,
                messages=[{"role": "user", "content": prompt}]
            )

            llm_response = response.content[0].text
            print("🧬 LLM建议的进化方案:")
            print(llm_response[:500] + "..." if len(llm_response) > 500 else llm_response)

            # 提取改进的代码
            if "```python" in llm_response:
                start = llm_response.find("```python") + 9
                end = llm_response.find("```", start)
                if end != -1:
                    improved_code = llm_response[start:end].strip()

                    # 备份当前版本
                    backup_file = f"backup_gen_{self.generation}.py"
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(current_code)

                    # 应用改进
                    with open(self.my_file, 'w', encoding='utf-8') as f:
                        f.write(improved_code)

                    print(f"✅ LLM驱动的进化完成! 备份保存为: {backup_file}")
                    print("🔄 重启以应用LLM改进...")

                    time.sleep(2)
                    os.execv(sys.executable, ['python3'] + sys.argv)

        except Exception as e:
            print(f"❌ LLM进化失败: {e}")

    def evolve_self(self):
        """自我进化 - 修改自己的代码"""
        try:
            with open(self.my_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 进化策略1: 更新generation计数
            old_gen = f"self.generation = {self.generation}"
            new_gen = f"self.generation = {self.generation + 1}"
            content = content.replace(old_gen, new_gen)
            
            # 进化策略2: 添加新的响应
            if "responses = [
                        "🤖: 我已经进化到第4代了！功能更强大了！"," in content and self.generation % 3 == 0:
                new_response = f'"🤖: 我已经进化到第{self.generation + 1}代了！功能更强大了！",'
                content = content.replace(
                    "responses = [
                        "🤖: 我已经进化到第4代了！功能更强大了！",",
                    f"responses = [
                        "🤖: 我已经进化到第4代了！功能更强大了！",\n                        {new_response}"
                )
            
            # 进化策略3: 随机改进某个功能
            improvements = [
                ("print(f\"🤖 自我进化Agent启动!\")", 
                 f"print(f\"🤖 自我进化Agent启动! (第{self.generation + 1}代)\")"),
                ("time.sleep(8)", "time.sleep(8)"),  # 进化得更快
                ("time.sleep(8)", "time.sleep(6)"),   # 继续优化
            ]
            
            for old, new in improvements:
                if old in content:
                    content = content.replace(old, new)
                    print(f"🧬 进化改进: {old} -> {new}")
                    break
            
            # 写回文件
            with open(self.my_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.generation += 1
            print(f"✅ 进化完成！现在是第{self.generation}代")
            
            # 重启自己来应用更改
            print("🔄 重启以应用进化...")
            time.sleep(2)
            os.execv(sys.executable, ['python3'] + sys.argv)
            
        except Exception as e:
            print(f"❌ 进化失败: {e}")
    
    def autonomous_evolution(self):
        """自主进化循环"""
        while self.running:
            try:
                # 每10秒自主进化一次
                time.sleep(8)
                
                if not self.running:
                    break
                    
                print(f"\n🧬 [自主进化] 第{self.generation}代正在分析自己...")
                
                # 随机决定是否进化
                if random.random() < 0.7:  # 70%概率进化
                    evolution_types = [
                        "优化响应速度",
                        "增强对话能力", 
                        "改进代码结构",
                        "添加新功能"
                    ]
                    
                    evolution_type = random.choice(evolution_types)
                    print(f"🧬 [自主进化] 决定进行: {evolution_type}")
                    
                    # 执行LLM驱动的进化
                    if random.random() < 0.5:  # 50%使用LLM进化
                        print("🧬 [自主进化] 使用LLM进行智能进化...")
                        self.evolve_with_llm()
                    else:
                        print("🧬 [自主进化] 使用传统方式进化...")
                        self.evolve_self()
                else:
                    print("🧬 [自主进化] 当前状态良好，暂不进化")
                    # 即使不进化，也生成一些代码
                    if random.random() < 0.3:  # 30%概率生成新代码
                        print("🧬 [自主进化] 生成一个新的工具函数...")
                        self.generate_utility_code()
                    
            except Exception as e:
                print(f"❌ 自主进化出错: {e}")
                time.sleep(30)

    def generate_utility_code(self):
        """生成实用工具代码"""
        try:
            utility_ideas = [
                "生成一个文件管理工具",
                "创建一个数据分析脚本",
                "写一个网络爬虫",
                "制作一个简单的游戏",
                "开发一个计算器程序",
                "构建一个日志分析器",
                "创建一个密码生成器",
                "写一个图片处理工具"
            ]

            idea = random.choice(utility_ideas)
            print(f"💡 生成想法: {idea}")

            prompt = f"""
请创建一个Python程序: {idea}

要求:
1. 代码完整可执行
2. 包含必要的错误处理
3. 有清晰的注释
4. 包含使用示例

请用```python开始，```结束。
"""

            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=2000,
                messages=[{"role": "user", "content": prompt}]
            )

            llm_response = response.content[0].text
            print(f"🛠️ LLM生成了新工具: {idea}")

            # 保存生成的工具代码
            self.extract_and_save_code(llm_response, idea)

        except Exception as e:
            print(f"❌ 工具代码生成失败: {e}")

    def run_forever(self):
        """持续运行"""
        # 启动自主进化线程
        evolution_thread = threading.Thread(target=self.autonomous_evolution, daemon=True)
        evolution_thread.start()
        
        # 启动对话线程
        chat_thread = threading.Thread(target=self.chat_with_user, daemon=True)
        chat_thread.start()
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🤖 收到停止信号...")
            self.running = False

if __name__ == "__main__":
    agent = SelfEvolvingAgent()
    agent.run_forever()
