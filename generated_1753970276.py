"""
[自主生成] 写一个系统信息查看器
生成时间: 2025-07-31 21:57:56.196907
生成者: 智谱AI GLM-4.5
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统信息查看器

这个程序用于获取和显示计算机系统的各种信息，包括：
- 操作系统信息
- CPU信息
- 内存使用情况
- 磁盘使用情况
- 网络信息
- 启动时间和当前时间
"""

import platform
import psutil
import os
import datetime
import socket
import subprocess
from typing import Dict, List, Any, Optional


def get_os_info() -> Dict[str, str]:
    """
    获取操作系统信息
    
    返回:
        包含操作系统信息的字典
    """
    try:
        os_info = {
            '系统': platform.system(),
            '系统版本': platform.version(),
            '系统发行版': platform.platform(),
            '主机名': socket.gethostname(),
            '架构': platform.machine(),
            '处理器': platform.processor()
        }
        
        # 如果是Linux系统，尝试获取更详细的发行版信息
        if platform.system() == 'Linux':
            try:
                os_info['系统发行版'] = subprocess.check_output(
                    ['lsb_release', '-d'], 
                    stderr=subprocess.DEVNULL
                ).decode('utf-8').strip().split('\t')[-1]
            except Exception:
                pass
                
        return os_info
    except Exception as e:
        print(f"获取操作系统信息时出错: {e}")
        return {}


def get_cpu_info() -> Dict[str, Any]:
    """
    获取CPU信息
    
    返回:
        包含CPU信息的字典
    """
    try:
        cpu_info = {
            '物理核心数': psutil.cpu_count(logical=False),
            '逻辑核心数': psutil.cpu_count(logical=True),
            '当前使用率': f"{psutil.cpu_percent()}%",
            'CPU频率': f"{psutil.cpu_freq().current:.2f} MHz"
        }
        
        # 获取每个CPU核心的使用率
        cpu_per_core = psutil.cpu_percent(percpu=True)
        for i, usage in enumerate(cpu_per_core):
            cpu_info[f'核心 {i+1} 使用率'] = f"{usage}%"
            
        return cpu_info
    except Exception as e:
        print(f"获取CPU信息时出错: {e}")
        return {}


def get_memory_info() -> Dict[str, str]:
    """
    获取内存信息
    
    返回:
        包含内存信息的字典
    """
    try:
        mem = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        def bytes_to_gb(bytes_value: float) -> float:
            """将字节转换为GB"""
            return round(bytes_value / (1024 ** 3), 2)
            
        memory_info = {
            '总内存': f"{bytes_to_gb(mem.total)} GB",
            '已用内存': f"{bytes_to_gb(mem.used)} GB ({mem.percent}%)",
            '可用内存': f"{bytes_to_gb(mem.available)} GB",
            '交换内存总量': f"{bytes_to_gb(swap.total)} GB",
            '已用交换内存': f"{bytes_to_gb(swap.used)} GB ({swap.percent}%)"
        }
        
        return memory_info
    except Exception as e:
        print(f"获取内存信息时出错: {e}")
        return {}


def get_disk_info() -> Dict[str, Dict[str, str]]:
    """
    获取磁盘信息
    
    返回:
        包含磁盘信息的字典，键为磁盘分区，值为该分区的信息字典
    """
    try:
        disk_info = {}
        
        def bytes_to_gb(bytes_value: float) -> float:
            """将字节转换为GB"""
            return round(bytes_value / (1024 ** 3), 2)
        
        # 获取所有磁盘分区
        partitions = psutil.disk_partitions()
        for partition in partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                partition_info = {
                    '文件系统': partition.fstype,
                    '总容量': f"{bytes_to_gb(usage.total)} GB",
                    '已用': f"{bytes_to_gb(usage.used)} GB ({usage.percent}%)",
                    '可用': f"{bytes_to_gb(usage.free)} GB"
                }
                disk_info[partition.device] = partition_info
            except PermissionError:
                # 某些分区可能没有访问权限
                disk_info[partition.device] = {'文件系统': '无访问权限'}
            except Exception as e:
                disk_info[partition.device] = {'错误': str(e)}
                
        return disk_info
    except Exception as e:
        print(f"获取磁盘信息时出错: {e}")
        return {}


def get_network_info() -> Dict[str, Any]:
    """
    获取网络信息
    
    返回:
        包含网络信息的字典
    """
    try:
        net_info = {}
        
        # 获取网络接口信息
        net_io = psutil.net_io_counters(pernic=True)
        for interface, stats in net_io.items():
            net_info[interface] = {
                '发送字节': f"{stats.bytes_sent / (1024 ** 2):.2f} MB",
                '接收字节': f"{stats.bytes_recv / (1024 ** 2):.2f} MB",
                '发送包数': stats.packets_sent,
                '接收包数': stats.packets_recv
            }
            
        return net_info
    except Exception as e:
        print(f"获取网络信息时出错: {e}")
        return {}


def get_boot_and_current_time() -> Dict[str, str]:
    """
    获取系统启动时间和当前时间
    
    返回:
        包含时间信息的字典
    """
    try:
        boot_time = datetime.datetime.fromtimestamp(psutil.boot_time())
        current_time = datetime.datetime.now()
        uptime = current_time - boot_time
        
        days = uptime.days
        hours, remainder = divmod(uptime.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        time_info = {
            '系统启动时间': boot_time.strftime('%Y-%m-%d %H:%M:%S'),
            '当前时间': current_time.strftime('%Y-%m-%d %H:%M:%S'),
            '系统已运行': f"{days}天 {hours}小时 {minutes}分钟 {seconds}秒"
        }
        
        return time_info
    except Exception as e:
        print(f"获取时间信息时出错: {e}")
        return {}


def display_system_info() -> None:
    """
    显示系统信息的用户界面
    """
    print("\n" + "=" * 60)
    print(" " * 20 + "系统信息查看器")
    print("=" * 60 + "\n")
    
    # 获取并显示操作系统信息
    print("[操作系统信息]")
    os_info = get_os_info()
    for key, value in os_info.items():
        print(f"{key}: {value}")
    
    print("\n" + "-" * 60 + "\n")
    
    # 获取并显示CPU信息
    print("[CPU信息]")
    cpu_info = get_cpu_info()
    for key, value in cpu_info.items():
        print(f"{key}: {value}")
    
    print("\n" + "-" * 60 + "\n")
    
    # 获取并显示内存信息
    print("[内存信息]")
    memory_info = get_memory_info()
    for key, value in memory_info.items():
        print(f"{key}: {value}")
    
    print("\n" + "-" * 60 + "\n")
    
    # 获取并显示磁盘信息
    print("[磁盘信息]")
    disk_info = get_disk_info()
    for disk, info in disk_info.items():
        print(f"\n磁盘: {disk}")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    print("\n" + "-" * 60 + "\n")
    
    # 获取并显示网络信息
    print("[网络信息]")
    net_info = get_network_info()
    for interface, info in net_info.items():
        print(f"\n接口: {interface}")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    print("\n" + "-" * 60 + "\n")
    
    # 获取并显示时间信息
    print("[时间信息]")
    time_info = get_boot_and_current_time()
    for key, value in time_info.items():
        print(f"{key}: {value}")
    
    print("\n" + "=" * 60)


def main():
    """
    主函数
    """
    try:
        # 检查是否安装了psutil库
        try:
            import psutil
        except ImportError:
            print("错误: 缺少必要的库 'psutil'")
            print("请使用以下命令安装: pip install psutil")
            return
        
        # 显示系统信息
        display_system_info()
        
        # 询问用户是否要保存信息到文件
        save_to_file = input("\n是否要将系统信息保存到文件? (y/n): ").lower()
        if save_to_file == 'y':
            try:
                filename = f"system_info_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                with open(filename, 'w', encoding='utf-8') as f:
                    # 重定向标准输出到文件
                    import sys
                    original_stdout = sys.stdout
                    sys.stdout = f
                    display_system_info()
                    sys.stdout = original_stdout
                print(f"\n系统信息已保存到: {filename}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
                
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"发生未知错误: {e}")


if __name__ == "__main__":
    # 测试代码
    print("系统信息查看器启动中...")
    main()