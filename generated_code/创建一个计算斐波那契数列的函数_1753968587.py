def fibonacci(n):
    """
    计算第n个斐波那契数
    
    Args:
        n (int): 要计算的斐波那契数的位置
    
    Returns:
        int: 第n个斐波那契数
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n - 1) + <PERSON><PERSON><PERSON><PERSON>(n - 2)


def fibonacci_iterative(n):
    """
    使用迭代方法计算斐波那契数（更高效）
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b


# 测试代码
if __name__ == "__main__":
    for i in range(10):
        print(f"fibonacci({i}) = {fibonacci(i)}")
