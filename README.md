# Self-Coding Agent (EVO)

一个能够根据命令自动生成和执行代码的智能agent，支持不间断运行。

## 功能特性

- 🤖 **自动代码生成**: 根据自然语言描述生成Python代码
- 🔒 **安全执行**: 在沙箱环境中安全执行生成的代码
- 📁 **文件监控**: 通过监控JSON命令文件接收指令
- 📊 **实时日志**: 完整的日志记录和状态监控
- ⚡ **持续运行**: 支持守护进程模式，不间断运行

## 快速开始

### 1. 安装依赖

```bash
# 使用uv安装依赖
uv sync
```

### 2. 启动Agent

```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动agent
python src/main.py
```

### 3. 发送命令

在 `commands/` 目录下创建JSON命令文件：

```json
{
  "command": "generate_code",
  "description": "创建一个计算阶乘的函数",
  "requirements": [
    "函数名为factorial",
    "接受一个整数参数n",
    "返回n的阶乘"
  ],
  "priority": 1,
  "timeout": 30
}
```

## 命令格式

### 支持的命令类型

1. **generate_code**: 生成代码
2. **execute_code**: 执行现有代码
3. **generate_and_execute**: 生成并执行代码
4. **status**: 查看agent状态
5. **stop**: 停止agent

### 命令参数

- `command`: 命令类型（必需）
- `description`: 功能描述（必需）
- `requirements`: 具体需求列表（可选）
- `priority`: 优先级，1-10（可选，默认1）
- `timeout`: 执行超时时间，秒（可选，默认30）

## 使用示例

### 生成斐波那契函数

```json
{
  "command": "generate_code",
  "description": "创建斐波那契数列函数",
  "requirements": [
    "函数名为fibonacci",
    "支持递归和迭代两种实现",
    "包含测试代码"
  ]
}
```

### 生成排序算法

```json
{
  "command": "generate_and_execute",
  "description": "实现快速排序算法",
  "requirements": [
    "函数名为quick_sort",
    "接受列表参数",
    "返回排序后的列表",
    "包含测试用例"
  ]
}
```

### 查看状态

```json
{
  "command": "status",
  "description": "查看agent运行状态"
}
```

## 命令行选项

```bash
python src/main.py --help

选项:
  --command-dir DIR     命令文件目录 (默认: commands)
  --output-dir DIR      生成代码保存目录 (默认: generated_code)
  --sandbox-dir DIR     代码执行沙箱目录 (默认: sandbox)
  --timeout SECONDS     代码执行超时时间 (默认: 30)
  --daemon              以守护进程模式运行
  --test                测试模式运行
```

## 目录结构

```
evo/
├── src/
│   ├── agent/
│   │   ├── __init__.py
│   │   ├── core.py              # 主agent类
│   │   ├── command_receiver.py  # 命令接收器
│   │   ├── code_generator.py    # 代码生成器
│   │   ├── executor.py          # 安全执行器
│   │   └── logger.py            # 日志系统
│   └── main.py                  # 程序入口
├── commands/                    # 命令文件目录
│   ├── processed/               # 已处理的命令
│   └── example_command.json     # 示例命令
├── generated_code/              # 生成的代码文件
├── logs/                        # 日志文件
├── sandbox/                     # 代码执行沙箱
└── README.md
```

## 安全特性

- **沙箱执行**: 代码在隔离环境中执行
- **资源限制**: 限制执行时间和内存使用
- **危险操作检测**: 阻止文件系统、网络等危险操作
- **权限控制**: 限制可用的内置函数和模块

## 日志监控

Agent会在以下位置记录日志：
- 控制台输出: 实时状态信息
- 日志文件: `logs/selfcodingagent_YYYYMMDD.log`

## 扩展功能

### 集成LLM API

可以通过设置环境变量来集成大语言模型API：

```bash
export ANTHROPIC_BASE_URL=https://api.anthropic.com
export ANTHROPIC_AUTH_TOKEN=your_token_here
```

### 自定义代码模板

在 `code_generator.py` 中可以添加更多代码模板和生成策略。

## 故障排除

### 常见问题

1. **Agent无法启动**
   - 检查Python版本 (需要3.13+)
   - 确认依赖已正确安装

2. **命令文件不被处理**
   - 确认JSON格式正确
   - 检查文件权限
   - 查看日志文件获取详细错误信息

3. **代码执行失败**
   - 检查生成的代码是否包含被禁止的操作
   - 调整执行超时时间
   - 查看沙箱目录权限

## 开发计划

- [ ] 集成更多LLM API
- [ ] 支持更多编程语言
- [ ] Web界面管理
- [ ] 分布式执行
- [ ] 代码版本管理
- [ ] 性能优化建议

## 许可证

MIT License
