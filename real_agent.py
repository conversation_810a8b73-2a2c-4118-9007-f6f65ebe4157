#!/usr/bin/env python3
"""
真正的LLM驱动自我进化Agent
"""

import os
import sys
import time
import threading
import subprocess
from datetime import datetime
from anthropic import Anthropic

class RealEvolvingAgent:
    def __init__(self):
        self.running = True
        self.generation = 1
        self.client = Anthropic(
            base_url="https://open.bigmodel.cn/api/anthropic",
            api_key="bf0a58e76b0c43ec8d816c094e199f44.jdTgNSwu2hRizB6o"
        )
        
        print("🤖 真正的LLM驱动自我进化Agent启动!")
        print("🧠 已连接到你的LLM API")
        print("我会真正生成新代码并不断进化!")
        print("\n输入任何内容开始对话...")
        
    def chat_loop(self):
        """对话循环"""
        while self.running:
            try:
                user_input = input("\n你: ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    print("🤖: 再见！我会继续进化...")
                    self.running = False
                    break
                
                # 使用LLM回应
                self.respond_with_llm(user_input)
                
            except (KeyboardInterrupt, EOFError):
                print("\n🤖: 收到停止信号...")
                self.running = False
                break
    
    def respond_with_llm(self, user_input):
        """使用LLM回应用户"""
        try:
            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1500,
                messages=[{
                    "role": "user",
                    "content": f"""
你是一个自我进化的AI Agent。用户说: "{user_input}"

如果用户想要代码，就生成完整的Python代码。
如果用户想聊天，就正常对话。
如果用户提到进化，就说你会立即进化。

请简洁回应，如果生成代码请用```python包围。
"""
                }]
            )
            
            llm_response = response.content[0].text
            print(f"\n🤖: {llm_response}")
            
            # 如果包含代码，保存并执行
            if "```python" in llm_response:
                self.save_and_run_code(llm_response, user_input)
                
        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
    
    def save_and_run_code(self, llm_response, description):
        """保存并执行LLM生成的代码"""
        try:
            # 提取代码
            start = llm_response.find("```python") + 9
            end = llm_response.find("```", start)
            if end == -1:
                return
                
            code = llm_response[start:end].strip()
            
            # 保存代码
            timestamp = int(time.time())
            filename = f"generated_{timestamp}.py"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f'"""\n{description}\n生成时间: {datetime.now()}\n"""\n\n')
                f.write(code)
            
            print(f"✅ 代码已保存: {filename}")
            
            # 执行代码
            print("🚀 执行生成的代码...")
            try:
                result = subprocess.run([sys.executable, filename], 
                                      capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    print("✅ 执行成功!")
                    if result.stdout:
                        print("输出:", result.stdout)
                else:
                    print("❌ 执行失败:", result.stderr)
            except Exception as e:
                print(f"❌ 执行出错: {e}")
                
        except Exception as e:
            print(f"❌ 代码处理失败: {e}")
    
    def autonomous_evolution(self):
        """自主进化循环"""
        evolution_count = 0
        
        while self.running:
            try:
                time.sleep(15)  # 每15秒检查一次
                
                if not self.running:
                    break
                
                evolution_count += 1
                print(f"\n🧬 [自主进化 #{evolution_count}] 正在思考新的代码想法...")
                
                # 生成新代码
                self.generate_autonomous_code()
                
            except Exception as e:
                print(f"❌ 自主进化出错: {e}")
                time.sleep(30)
    
    def generate_autonomous_code(self):
        """自主生成代码"""
        try:
            ideas = [
                "创建一个随机密码生成器",
                "写一个简单的文件整理工具", 
                "制作一个数字猜谜游戏",
                "开发一个简单的计算器",
                "创建一个文本分析工具",
                "写一个时间管理助手",
                "制作一个简单的爬虫",
                "开发一个日志分析器"
            ]
            
            import random
            idea = random.choice(ideas)
            
            print(f"💡 自主想法: {idea}")
            
            response = self.client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=2000,
                messages=[{
                    "role": "user",
                    "content": f"""
请创建一个Python程序: {idea}

要求:
1. 代码完整可执行
2. 有清晰注释
3. 包含使用示例
4. 有基本的错误处理

请用```python开始，```结束。
"""
                }]
            )
            
            llm_response = response.content[0].text
            print(f"🛠️ LLM自主生成了: {idea}")
            
            # 保存并执行
            self.save_and_run_code(llm_response, f"[自主生成] {idea}")
            
        except Exception as e:
            print(f"❌ 自主代码生成失败: {e}")
    
    def run(self):
        """启动agent"""
        # 启动自主进化线程
        evolution_thread = threading.Thread(target=self.autonomous_evolution, daemon=True)
        evolution_thread.start()
        
        # 启动对话循环
        self.chat_loop()

if __name__ == "__main__":
    agent = RealEvolvingAgent()
    agent.run()
