#!/usr/bin/env python3
"""
Self-coding agent main entry point.
"""

import sys
import signal
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from agent.core import SelfCodingAgent
from agent.logger import agent_logger


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    agent_logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Self-coding agent")
    parser.add_argument(
        "--command-dir", 
        default="commands",
        help="Directory to monitor for commands (default: commands)"
    )
    parser.add_argument(
        "--output-dir",
        default="generated_code", 
        help="Directory to save generated code (default: generated_code)"
    )
    parser.add_argument(
        "--sandbox-dir",
        default="sandbox",
        help="Directory for safe code execution (default: sandbox)"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="Code execution timeout in seconds (default: 30)"
    )
    parser.add_argument(
        "--daemon",
        action="store_true",
        help="Run as daemon process"
    )
    parser.add_argument(
        "--test",
        action="store_true",
        help="Run in test mode (process one command and exit)"
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create agent
    agent = SelfCodingAgent(
        command_dir=args.command_dir,
        output_dir=args.output_dir,
        sandbox_dir=args.sandbox_dir,
        execution_timeout=args.timeout
    )
    
    try:
        if args.test:
            # Test mode - start agent, wait a bit, then stop
            agent_logger.info("Running in test mode")
            agent.start()
            
            import time
            time.sleep(5)  # Wait 5 seconds for any commands
            
            stats = agent.get_stats()
            agent_logger.info("Test completed", stats)
            agent.stop()
            
        elif args.daemon:
            # Daemon mode - run as background process
            agent_logger.info("Starting agent in daemon mode")
            
            # For now, just run normally
            # In production, you'd use python-daemon library
            agent.run_forever()
            
        else:
            # Normal mode - run interactively
            agent_logger.info("Starting agent in interactive mode")
            agent_logger.info("Press Ctrl+C to stop the agent")
            
            agent.run_forever()
            
    except KeyboardInterrupt:
        agent_logger.info("Keyboard interrupt received")
    except Exception as e:
        agent_logger.error(f"Unexpected error: {e}")
    finally:
        if agent.is_running:
            agent.stop()
        agent_logger.info("Agent shutdown complete")


if __name__ == "__main__":
    main()
