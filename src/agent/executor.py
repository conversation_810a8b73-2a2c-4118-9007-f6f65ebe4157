"""
Safe code executor for the self-coding agent.
Executes generated code in a controlled environment.
"""

import os
import sys
import subprocess
import tempfile
import time
import signal
import psutil
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import contextmanager
from .logger import agent_logger


class ExecutionResult:
    """Container for code execution results."""
    
    def __init__(self, success: bool, output: str = "", error: str = "", 
                 execution_time: float = 0.0, memory_used: float = 0.0):
        self.success = success
        self.output = output
        self.error = error
        self.execution_time = execution_time
        self.memory_used = memory_used
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'success': self.success,
            'output': self.output,
            'error': self.error,
            'execution_time': self.execution_time,
            'memory_used': self.memory_used
        }


class SafeExecutor:
    """Safe code executor with resource limits and sandboxing."""
    
    def __init__(self, sandbox_dir: str = "sandbox", timeout: int = 30, 
                 max_memory_mb: int = 100):
        self.sandbox_dir = Path(sandbox_dir)
        self.sandbox_dir.mkdir(exist_ok=True)
        self.timeout = timeout
        self.max_memory_mb = max_memory_mb
        
        # Dangerous modules/functions to block
        self.blocked_imports = {
            'os', 'sys', 'subprocess', 'shutil', 'glob', 'socket', 
            'urllib', 'requests', 'http', 'ftplib', 'smtplib',
            'pickle', 'marshal', 'shelve', 'dbm', 'sqlite3',
            'ctypes', 'multiprocessing', 'threading', 'asyncio'
        }
        
        # Allowed built-in functions
        self.allowed_builtins = {
            'abs', 'all', 'any', 'bin', 'bool', 'chr', 'dict', 'dir',
            'divmod', 'enumerate', 'filter', 'float', 'format', 'hex',
            'int', 'isinstance', 'len', 'list', 'map', 'max', 'min',
            'oct', 'ord', 'pow', 'print', 'range', 'reversed', 'round',
            'set', 'sorted', 'str', 'sum', 'tuple', 'type', 'zip'
        }
    
    def execute_code(self, code: str, filename: Optional[str] = None) -> ExecutionResult:
        """
        Execute Python code safely in a sandboxed environment.
        
        Args:
            code: Python code to execute
            filename: Optional filename for the code
            
        Returns:
            ExecutionResult: Execution results
        """
        agent_logger.info("Starting code execution")
        
        try:
            # Validate code safety
            if not self._is_code_safe(code):
                return ExecutionResult(
                    success=False,
                    error="Code contains potentially dangerous operations"
                )
            
            # Create temporary file for execution
            with tempfile.NamedTemporaryFile(
                mode='w', 
                suffix='.py', 
                dir=self.sandbox_dir,
                delete=False
            ) as temp_file:
                temp_file.write(code)
                temp_filename = temp_file.name
            
            try:
                # Execute the code
                result = self._execute_file(temp_filename)
                
                # Log execution result
                agent_logger.log_code_executed(
                    result.success, 
                    result.output[:200], 
                    result.error[:200]
                )
                
                return result
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_filename)
                except OSError:
                    pass
                    
        except Exception as e:
            agent_logger.error(f"Code execution failed: {e}")
            return ExecutionResult(
                success=False,
                error=f"Execution error: {str(e)}"
            )
    
    def _is_code_safe(self, code: str) -> bool:
        """
        Check if code is safe to execute.
        
        Args:
            code: Python code to check
            
        Returns:
            bool: True if code appears safe
        """
        # Check for blocked imports
        lines = code.split('\n')
        for line in lines:
            line = line.strip()
            
            # Check import statements
            if line.startswith('import ') or line.startswith('from '):
                for blocked in self.blocked_imports:
                    if blocked in line:
                        agent_logger.warning(f"Blocked import detected: {line}")
                        return False
            
            # Check for dangerous function calls
            dangerous_patterns = [
                'exec(', 'eval(', 'compile(', '__import__(',
                'open(', 'file(', 'input(', 'raw_input(',
                'exit(', 'quit(', 'reload('
            ]
            
            for pattern in dangerous_patterns:
                if pattern in line:
                    agent_logger.warning(f"Dangerous pattern detected: {pattern}")
                    return False
        
        return True
    
    def _execute_file(self, filename: str) -> ExecutionResult:
        """
        Execute a Python file with resource monitoring.
        
        Args:
            filename: Path to Python file to execute
            
        Returns:
            ExecutionResult: Execution results
        """
        start_time = time.time()
        
        try:
            # Start the process
            process = subprocess.Popen(
                [sys.executable, filename],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.sandbox_dir,
                preexec_fn=os.setsid if os.name != 'nt' else None
            )
            
            # Monitor process
            try:
                stdout, stderr = process.communicate(timeout=self.timeout)
                execution_time = time.time() - start_time
                
                # Get memory usage (approximate)
                memory_used = 0.0
                try:
                    proc = psutil.Process(process.pid)
                    memory_used = proc.memory_info().rss / 1024 / 1024  # MB
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                
                success = process.returncode == 0
                
                return ExecutionResult(
                    success=success,
                    output=stdout,
                    error=stderr,
                    execution_time=execution_time,
                    memory_used=memory_used
                )
                
            except subprocess.TimeoutExpired:
                # Kill the process group
                if os.name != 'nt':
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                else:
                    process.terminate()
                
                process.wait()
                
                return ExecutionResult(
                    success=False,
                    error=f"Execution timed out after {self.timeout} seconds",
                    execution_time=self.timeout
                )
                
        except Exception as e:
            return ExecutionResult(
                success=False,
                error=f"Process execution error: {str(e)}",
                execution_time=time.time() - start_time
            )
    
    def execute_code_string(self, code: str) -> ExecutionResult:
        """
        Execute code string directly (for simple expressions).
        
        Args:
            code: Python code string
            
        Returns:
            ExecutionResult: Execution results
        """
        if not self._is_code_safe(code):
            return ExecutionResult(
                success=False,
                error="Code contains potentially dangerous operations"
            )
        
        start_time = time.time()
        
        try:
            # Capture stdout
            from io import StringIO
            import contextlib
            
            stdout_capture = StringIO()
            stderr_capture = StringIO()
            
            with contextlib.redirect_stdout(stdout_capture), \
                 contextlib.redirect_stderr(stderr_capture):
                
                # Create restricted globals
                restricted_globals = {
                    '__builtins__': {name: getattr(__builtins__, name) 
                                   for name in self.allowed_builtins 
                                   if hasattr(__builtins__, name)}
                }
                
                # Execute code
                exec(code, restricted_globals)
            
            execution_time = time.time() - start_time
            
            return ExecutionResult(
                success=True,
                output=stdout_capture.getvalue(),
                error=stderr_capture.getvalue(),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return ExecutionResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def test_execution_environment(self) -> bool:
        """
        Test if the execution environment is working properly.
        
        Returns:
            bool: True if environment is working
        """
        test_code = '''
print("Hello, World!")
result = 2 + 2
print(f"2 + 2 = {result}")
'''
        
        result = self.execute_code(test_code)
        
        if result.success and "Hello, World!" in result.output:
            agent_logger.info("Execution environment test passed")
            return True
        else:
            agent_logger.error("Execution environment test failed")
            return False
