"""
Command receiver for the self-coding agent.
Monitors command files and processes user commands.
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from pydantic import BaseModel, ValidationError
from .logger import agent_logger


class Command(BaseModel):
    """Command model for validation."""
    command: str
    description: str
    requirements: list[str] = []
    timestamp: Optional[str] = None
    priority: int = 1
    timeout: int = 30


class CommandHandler(FileSystemEventHandler):
    """File system event handler for command files."""
    
    def __init__(self, callback: Callable[[Command], None]):
        self.callback = callback
        self.processed_files = set()
    
    def on_created(self, event):
        """Handle file creation events."""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix.lower() == '.json' and file_path.name not in self.processed_files:
            # Wait a bit to ensure file is fully written
            time.sleep(0.1)
            self._process_command_file(file_path)
    
    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        if file_path.suffix.lower() == '.json' and file_path.name not in self.processed_files:
            # Wait a bit to ensure file is fully written
            time.sleep(0.1)
            self._process_command_file(file_path)
    
    def _process_command_file(self, file_path: Path):
        """Process a command file."""
        try:
            agent_logger.debug(f"Processing command file: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                command_data = json.load(f)
            
            # Validate command
            command = Command(**command_data)
            
            # Mark as processed
            self.processed_files.add(file_path.name)
            
            # Log command received
            agent_logger.log_command_received(command.dict())
            
            # Execute callback
            self.callback(command)
            
            # Move processed file to avoid reprocessing
            processed_dir = file_path.parent / "processed"
            processed_dir.mkdir(exist_ok=True)
            
            processed_path = processed_dir / f"{int(time.time())}_{file_path.name}"
            file_path.rename(processed_path)
            
            agent_logger.info(f"Command file processed and moved to: {processed_path}")
            
        except json.JSONDecodeError as e:
            agent_logger.error(f"Invalid JSON in command file {file_path}: {e}")
        except ValidationError as e:
            agent_logger.error(f"Invalid command format in {file_path}: {e}")
        except Exception as e:
            agent_logger.error(f"Error processing command file {file_path}: {e}")


class CommandReceiver:
    """Command receiver that monitors for new commands."""
    
    def __init__(self, command_dir: str = "commands", callback: Optional[Callable[[Command], None]] = None):
        self.command_dir = Path(command_dir)
        self.command_dir.mkdir(exist_ok=True)
        
        self.callback = callback
        self.observer = Observer()
        self.handler = CommandHandler(self._handle_command)
        self.is_running = False
    
    def set_callback(self, callback: Callable[[Command], None]):
        """Set the callback function for handling commands."""
        self.callback = callback
        self.handler.callback = self._handle_command
    
    def _handle_command(self, command: Command):
        """Handle received command."""
        if self.callback:
            try:
                self.callback(command)
            except Exception as e:
                agent_logger.error(f"Error in command callback: {e}")
        else:
            agent_logger.warning("No callback set for command handling")
    
    def start(self):
        """Start monitoring for commands."""
        if self.is_running:
            agent_logger.warning("Command receiver is already running")
            return
        
        agent_logger.info(f"Starting command receiver, monitoring: {self.command_dir}")
        
        self.observer.schedule(self.handler, str(self.command_dir), recursive=False)
        self.observer.start()
        self.is_running = True
        
        # Process any existing command files
        self._process_existing_files()
    
    def stop(self):
        """Stop monitoring for commands."""
        if not self.is_running:
            return
        
        agent_logger.info("Stopping command receiver")
        self.observer.stop()
        self.observer.join()
        self.is_running = False
    
    def _process_existing_files(self):
        """Process any existing command files in the directory."""
        for file_path in self.command_dir.glob("*.json"):
            if file_path.name not in self.handler.processed_files:
                self.handler._process_command_file(file_path)
    
    def create_example_command(self):
        """Create an example command file for reference."""
        example_command = {
            "command": "generate_code",
            "description": "创建一个计算斐波那契数列的函数",
            "requirements": [
                "函数名为fibonacci",
                "接受一个整数参数n",
                "返回第n个斐波那契数",
                "使用递归实现"
            ],
            "priority": 1,
            "timeout": 30
        }
        
        example_file = self.command_dir / "example_command.json"
        with open(example_file, 'w', encoding='utf-8') as f:
            json.dump(example_command, f, ensure_ascii=False, indent=2)
        
        agent_logger.info(f"Example command file created: {example_file}")
