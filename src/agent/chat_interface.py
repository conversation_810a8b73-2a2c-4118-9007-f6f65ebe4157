"""
Chat interface for the self-evolving agent.
Provides real-time conversation capabilities.
"""

import asyncio
import threading
import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.live import Live
from rich.layout import Layout
from rich.prompt import Prompt
from rich.markdown import Markdown
from .logger import agent_logger


class Message:
    """Represents a chat message."""
    
    def __init__(self, sender: str, content: str, timestamp: Optional[datetime] = None):
        self.sender = sender
        self.content = content
        self.timestamp = timestamp or datetime.now()
        self.message_id = f"{sender}_{int(time.time() * 1000)}"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'sender': self.sender,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'message_id': self.message_id
        }


class ConversationHistory:
    """Manages conversation history and context."""
    
    def __init__(self, max_messages: int = 100):
        self.messages: List[Message] = []
        self.max_messages = max_messages
        self.context_summary = ""
    
    def add_message(self, message: Message):
        """Add a message to history."""
        self.messages.append(message)
        
        # Keep only recent messages
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def get_recent_messages(self, count: int = 10) -> List[Message]:
        """Get recent messages."""
        return self.messages[-count:]
    
    def get_context_for_ai(self) -> str:
        """Get conversation context formatted for AI."""
        recent = self.get_recent_messages(5)
        context = []
        
        for msg in recent:
            context.append(f"{msg.sender}: {msg.content}")
        
        return "\n".join(context)


class ChatInterface:
    """Interactive chat interface for the agent."""
    
    def __init__(self, agent_name: str = "EvoAgent"):
        self.agent_name = agent_name
        self.console = Console()
        self.history = ConversationHistory()
        self.is_running = False
        self.input_thread = None
        self.message_callback: Optional[Callable[[Message], None]] = None
        
        # Chat state
        self.waiting_for_input = False
        self.current_input = ""
        
    def set_message_callback(self, callback: Callable[[Message], None]):
        """Set callback for handling user messages."""
        self.message_callback = callback
    
    def start(self):
        """Start the chat interface."""
        if self.is_running:
            return
        
        self.is_running = True
        agent_logger.info("Starting chat interface")
        
        # Display welcome message
        self._display_welcome()
        
        # Start input handling thread
        self.input_thread = threading.Thread(target=self._input_handler, daemon=True)
        self.input_thread.start()
        
        # Send initial agent message
        self.send_agent_message(
            "🤖 你好！我是EvoAgent，一个能够自我进化的AI助手。\n"
            "我不仅能执行你的命令，还会主动学习和改进自己。\n"
            "你可以随时与我对话，我会根据我们的交流不断进化！\n\n"
            "输入 'help' 查看我能做什么，或者直接告诉我你想要什么功能。"
        )
    
    def stop(self):
        """Stop the chat interface."""
        if not self.is_running:
            return
        
        self.is_running = False
        agent_logger.info("Stopping chat interface")
    
    def _display_welcome(self):
        """Display welcome screen."""
        welcome_text = Text()
        welcome_text.append("🧠 ", style="bold blue")
        welcome_text.append("EvoAgent", style="bold cyan")
        welcome_text.append(" - 自我进化AI助手", style="bold white")
        
        welcome_panel = Panel(
            welcome_text,
            title="🚀 启动中...",
            border_style="cyan",
            padding=(1, 2)
        )
        
        self.console.print(welcome_panel)
        self.console.print()
    
    def _input_handler(self):
        """Handle user input in a separate thread."""
        while self.is_running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    f"[bold green]你[/bold green]",
                    console=self.console
                ).strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    self.send_agent_message("👋 再见！我会继续在后台进化，随时欢迎你回来！")
                    self.stop()
                    break
                
                # Create and process message
                message = Message("user", user_input)
                self.history.add_message(message)
                
                # Call callback if set
                if self.message_callback:
                    self.message_callback(message)
                
            except (KeyboardInterrupt, EOFError):
                self.stop()
                break
            except Exception as e:
                agent_logger.error(f"Error in input handler: {e}")
    
    def send_agent_message(self, content: str):
        """Send a message from the agent."""
        message = Message(self.agent_name, content)
        self.history.add_message(message)
        
        # Display the message
        self._display_agent_message(content)
    
    def _display_agent_message(self, content: str):
        """Display an agent message with nice formatting."""
        # Create agent message panel
        agent_panel = Panel(
            Markdown(content),
            title=f"🤖 {self.agent_name}",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(agent_panel)
        self.console.print()
    
    def send_system_message(self, content: str):
        """Send a system message."""
        system_panel = Panel(
            content,
            title="⚙️ 系统",
            border_style="yellow",
            padding=(1, 1)
        )
        
        self.console.print(system_panel)
        self.console.print()
    
    def send_evolution_message(self, content: str):
        """Send an evolution/improvement message."""
        evolution_panel = Panel(
            Markdown(content),
            title="🧬 进化中...",
            border_style="magenta",
            padding=(1, 2)
        )
        
        self.console.print(evolution_panel)
        self.console.print()
    
    def get_conversation_context(self) -> str:
        """Get current conversation context."""
        return self.history.get_context_for_ai()
    
    def display_stats(self, stats: Dict[str, Any]):
        """Display agent statistics."""
        stats_text = []
        for key, value in stats.items():
            if isinstance(value, float):
                stats_text.append(f"{key}: {value:.2f}")
            else:
                stats_text.append(f"{key}: {value}")
        
        stats_panel = Panel(
            "\n".join(stats_text),
            title="📊 Agent状态",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print(stats_panel)
        self.console.print()
    
    def display_code_generation(self, description: str, filename: str):
        """Display code generation notification."""
        code_panel = Panel(
            f"📝 **生成代码**: {description}\n💾 **文件**: {filename}",
            title="🔧 代码生成",
            border_style="cyan",
            padding=(1, 2)
        )
        
        self.console.print(code_panel)
        self.console.print()
    
    def display_evolution_progress(self, stage: str, progress: str):
        """Display evolution progress."""
        progress_panel = Panel(
            f"**阶段**: {stage}\n**进度**: {progress}",
            title="🧬 进化进度",
            border_style="magenta",
            padding=(1, 2)
        )
        
        self.console.print(progress_panel)
        self.console.print()
