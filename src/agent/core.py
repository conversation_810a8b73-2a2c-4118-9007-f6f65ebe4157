"""
Core self-coding agent implementation.
"""

import time
import threading
from typing import Dict, Any, Optional
from pathlib import Path

from .logger import agent_logger
from .command_receiver import CommandReceiver, Command
from .code_generator import CodeGenerator
from .executor import SafeExecutor, ExecutionResult


class SelfCodingAgent:
    """
    Main self-coding agent that can generate and execute code based on commands.
    """
    
    def __init__(self, 
                 command_dir: str = "commands",
                 output_dir: str = "generated_code",
                 sandbox_dir: str = "sandbox",
                 execution_timeout: int = 30):
        """
        Initialize the self-coding agent.
        
        Args:
            command_dir: Directory to monitor for commands
            output_dir: Directory to save generated code
            sandbox_dir: Directory for safe code execution
            execution_timeout: Timeout for code execution in seconds
        """
        self.command_dir = command_dir
        self.output_dir = output_dir
        self.sandbox_dir = sandbox_dir
        self.execution_timeout = execution_timeout
        
        # Initialize components
        self.command_receiver = CommandReceiver(command_dir)
        self.code_generator = CodeGenerator(output_dir)
        self.executor = SafeExecutor(sandbox_dir, execution_timeout)
        
        # Agent state
        self.is_running = False
        self.stats = {
            'commands_processed': 0,
            'code_generated': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'start_time': None,
            'uptime': 0
        }
        
        # Set up command callback
        self.command_receiver.set_callback(self._handle_command)
        
        agent_logger.info("Self-coding agent initialized")
    
    def start(self):
        """Start the agent and begin monitoring for commands."""
        if self.is_running:
            agent_logger.warning("Agent is already running")
            return
        
        agent_logger.log_agent_status("STARTING")
        
        # Test execution environment
        if not self.executor.test_execution_environment():
            agent_logger.error("Execution environment test failed. Agent cannot start.")
            return
        
        # Create example command file
        self.command_receiver.create_example_command()
        
        # Start command receiver
        self.command_receiver.start()
        
        # Update state
        self.is_running = True
        self.stats['start_time'] = time.time()
        
        # Start stats update thread
        self._start_stats_thread()
        
        agent_logger.log_agent_status("RUNNING")
        agent_logger.info(f"Agent started successfully. Monitoring: {self.command_dir}")
        agent_logger.info("Place JSON command files in the commands directory to interact with the agent")
    
    def stop(self):
        """Stop the agent."""
        if not self.is_running:
            agent_logger.warning("Agent is not running")
            return
        
        agent_logger.log_agent_status("STOPPING")
        
        # Stop command receiver
        self.command_receiver.stop()
        
        # Update state
        self.is_running = False
        
        agent_logger.log_agent_status("STOPPED")
        agent_logger.info("Agent stopped successfully")
    
    def _handle_command(self, command: Command):
        """
        Handle received command.
        
        Args:
            command: Command to process
        """
        agent_logger.info(f"Processing command: {command.command}")
        
        try:
            self.stats['commands_processed'] += 1
            
            if command.command == "generate_code":
                self._handle_generate_code_command(command)
            elif command.command == "execute_code":
                self._handle_execute_code_command(command)
            elif command.command == "generate_and_execute":
                self._handle_generate_and_execute_command(command)
            elif command.command == "status":
                self._handle_status_command(command)
            elif command.command == "stop":
                self._handle_stop_command(command)
            else:
                agent_logger.warning(f"Unknown command: {command.command}")
                
        except Exception as e:
            agent_logger.error(f"Error handling command: {e}")
    
    def _handle_generate_code_command(self, command: Command):
        """Handle code generation command."""
        agent_logger.info("Generating code...")
        
        # Generate code
        result = self.code_generator.generate_code(command)
        
        if result['success']:
            self.stats['code_generated'] += 1
            agent_logger.info(f"Code generated successfully: {result['filename']}")
            
            # Optionally execute the generated code
            if any('执行' in req or 'execute' in req.lower() for req in command.requirements):
                agent_logger.info("Auto-executing generated code...")
                exec_result = self.executor.execute_code(result['code'])
                self._log_execution_result(exec_result)
        else:
            agent_logger.error(f"Code generation failed: {result.get('error', 'Unknown error')}")
    
    def _handle_execute_code_command(self, command: Command):
        """Handle code execution command."""
        # This would execute existing code files
        # For now, we'll just log that this command type is not fully implemented
        agent_logger.info("Execute code command received (not fully implemented)")
    
    def _handle_generate_and_execute_command(self, command: Command):
        """Handle combined generate and execute command."""
        agent_logger.info("Generating and executing code...")
        
        # Generate code
        result = self.code_generator.generate_code(command)
        
        if result['success']:
            self.stats['code_generated'] += 1
            agent_logger.info(f"Code generated: {result['filename']}")
            
            # Execute the generated code
            exec_result = self.executor.execute_code(result['code'])
            self._log_execution_result(exec_result)
        else:
            agent_logger.error(f"Code generation failed: {result.get('error', 'Unknown error')}")
    
    def _handle_status_command(self, command: Command):
        """Handle status request command."""
        uptime = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0
        self.stats['uptime'] = uptime
        
        status_info = {
            'status': 'RUNNING' if self.is_running else 'STOPPED',
            'uptime_seconds': uptime,
            'uptime_formatted': self._format_uptime(uptime),
            'stats': self.stats.copy()
        }
        
        agent_logger.info("Agent Status", status_info)
    
    def _handle_stop_command(self, command: Command):
        """Handle stop command."""
        agent_logger.info("Stop command received")
        # Schedule stop in a separate thread to avoid blocking
        threading.Thread(target=self._delayed_stop, daemon=True).start()
    
    def _delayed_stop(self):
        """Stop the agent after a short delay."""
        time.sleep(1)  # Give time for the command to be processed
        self.stop()
    
    def _log_execution_result(self, result: ExecutionResult):
        """Log code execution result and update stats."""
        if result.success:
            self.stats['successful_executions'] += 1
            agent_logger.info(f"Code executed successfully in {result.execution_time:.2f}s")
            if result.output:
                agent_logger.info(f"Output: {result.output[:500]}")
        else:
            self.stats['failed_executions'] += 1
            agent_logger.error(f"Code execution failed: {result.error[:500]}")
    
    def _format_uptime(self, seconds: float) -> str:
        """Format uptime in human-readable format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def _start_stats_thread(self):
        """Start background thread for periodic stats updates."""
        def update_stats():
            while self.is_running:
                time.sleep(60)  # Update every minute
                if self.is_running and self.stats['start_time']:
                    uptime = time.time() - self.stats['start_time']
                    self.stats['uptime'] = uptime
                    
                    # Log periodic status
                    if int(uptime) % 300 == 0:  # Every 5 minutes
                        agent_logger.info(f"Agent running for {self._format_uptime(uptime)}")
        
        stats_thread = threading.Thread(target=update_stats, daemon=True)
        stats_thread.start()
    
    def run_forever(self):
        """
        Run the agent indefinitely.
        This method blocks until the agent is stopped.
        """
        if not self.is_running:
            self.start()
        
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            agent_logger.info("Keyboard interrupt received")
            self.stop()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current agent statistics."""
        if self.stats['start_time']:
            self.stats['uptime'] = time.time() - self.stats['start_time']
        return self.stats.copy()
