"""
Core self-coding agent implementation.
"""

import time
import threading
from typing import Dict, Any, Optional
from pathlib import Path

from .logger import agent_logger
from .command_receiver import CommandReceiver, Command
from .code_generator import CodeGenerator
from .executor import SafeExecutor, ExecutionResult
from .chat_interface import Cha<PERSON><PERSON>nterface, Message
from .conversation_manager import Conversation<PERSON>anager
from .performance_analyzer import PerformanceAnalyzer
from .evolution_engine import EvolutionEngine


class SelfCodingAgent:
    """
    Main self-coding agent that can generate and execute code based on commands.
    """
    
    def __init__(self,
                 command_dir: str = "commands",
                 output_dir: str = "generated_code",
                 sandbox_dir: str = "sandbox",
                 execution_timeout: int = 30,
                 enable_chat: bool = True,
                 enable_evolution: bool = True):
        """
        Initialize the self-evolving coding agent.

        Args:
            command_dir: Directory to monitor for commands
            output_dir: Directory to save generated code
            sandbox_dir: Directory for safe code execution
            execution_timeout: Timeout for code execution in seconds
            enable_chat: Enable chat interface
            enable_evolution: Enable autonomous evolution
        """
        self.command_dir = command_dir
        self.output_dir = output_dir
        self.sandbox_dir = sandbox_dir
        self.execution_timeout = execution_timeout
        self.enable_chat = enable_chat
        self.enable_evolution = enable_evolution

        # Initialize core components
        self.command_receiver = CommandReceiver(command_dir)
        self.code_generator = CodeGenerator(output_dir)
        self.executor = SafeExecutor(sandbox_dir, execution_timeout)

        # Initialize evolution components
        self.performance_analyzer = PerformanceAnalyzer()
        self.evolution_engine = EvolutionEngine(self.performance_analyzer, self.code_generator)

        # Initialize chat components
        if self.enable_chat:
            self.chat_interface = ChatInterface("EvoAgent")
            self.conversation_manager = ConversationManager()
            self.chat_interface.set_message_callback(self._handle_chat_message)

        # Agent state
        self.is_running = False
        self.stats = {
            'commands_processed': 0,
            'code_generated': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'conversations': 0,
            'evolution_cycles': 0,
            'start_time': None,
            'uptime': 0,
            'last_user_interaction': 0
        }

        # Set up callbacks
        self.command_receiver.set_callback(self._handle_command)

        agent_logger.info("Self-evolving coding agent initialized")
    
    def start(self):
        """Start the agent and begin monitoring for commands."""
        if self.is_running:
            agent_logger.warning("Agent is already running")
            return

        agent_logger.log_agent_status("STARTING")

        # Test execution environment
        if not self.executor.test_execution_environment():
            agent_logger.error("Execution environment test failed. Agent cannot start.")
            return

        # Start chat interface first
        if self.enable_chat:
            self.chat_interface.start()

        # Create example command file
        self.command_receiver.create_example_command()

        # Start command receiver
        self.command_receiver.start()

        # Start evolution engine
        if self.enable_evolution:
            self.evolution_engine.start_evolution()

        # Update state
        self.is_running = True
        self.stats['start_time'] = time.time()

        # Start background threads
        self._start_stats_thread()
        self._start_proactive_chat_thread()

        agent_logger.log_agent_status("RUNNING")

        if self.enable_chat:
            agent_logger.info("🤖 EvoAgent started with chat interface and autonomous evolution!")
        else:
            agent_logger.info(f"Agent started successfully. Monitoring: {self.command_dir}")
    
    def stop(self):
        """Stop the agent."""
        if not self.is_running:
            agent_logger.warning("Agent is not running")
            return

        agent_logger.log_agent_status("STOPPING")

        # Stop evolution engine
        if self.enable_evolution:
            self.evolution_engine.stop_evolution()

        # Stop chat interface
        if self.enable_chat:
            self.chat_interface.stop()

        # Stop command receiver
        self.command_receiver.stop()

        # Update state
        self.is_running = False

        agent_logger.log_agent_status("STOPPED")
        agent_logger.info("Agent stopped successfully")
    
    def _handle_command(self, command: Command):
        """
        Handle received command.
        
        Args:
            command: Command to process
        """
        agent_logger.info(f"Processing command: {command.command}")
        
        try:
            self.stats['commands_processed'] += 1
            
            if command.command == "generate_code":
                self._handle_generate_code_command(command)
            elif command.command == "execute_code":
                self._handle_execute_code_command(command)
            elif command.command == "generate_and_execute":
                self._handle_generate_and_execute_command(command)
            elif command.command == "status":
                self._handle_status_command(command)
            elif command.command == "stop":
                self._handle_stop_command(command)
            else:
                agent_logger.warning(f"Unknown command: {command.command}")
                
        except Exception as e:
            agent_logger.error(f"Error handling command: {e}")

    def _handle_chat_message(self, message: Message):
        """Handle chat message from user."""
        if not self.enable_chat:
            return

        try:
            self.stats['conversations'] += 1
            self.stats['last_user_interaction'] = time.time()

            # Record performance metric
            start_time = time.time()

            # Process message with conversation manager
            response_plan = self.conversation_manager.process_message(message)

            # Execute actions based on response plan
            if 'generate_code' in response_plan.get('actions', []):
                self._handle_chat_code_request(response_plan)
            elif 'get_status' in response_plan.get('actions', []):
                self._handle_chat_status_request()
            elif 'trigger_evolution' in response_plan.get('actions', []):
                self._handle_chat_evolution_request(response_plan)
            else:
                # Send text response
                self.chat_interface.send_agent_message(response_plan['response_text'])

            # Record response time
            response_time = time.time() - start_time
            self.performance_analyzer.record_metric('chat_response_time', response_time)

        except Exception as e:
            agent_logger.error(f"Error handling chat message: {e}")
            if self.enable_chat:
                self.chat_interface.send_agent_message(
                    "抱歉，我在处理你的消息时遇到了问题。让我分析一下这个错误并改进自己。"
                )

    def _handle_chat_code_request(self, response_plan: Dict[str, Any]):
        """Handle code generation request from chat."""
        code_params = response_plan.get('code_params', {})

        # Send initial response
        self.chat_interface.send_agent_message(response_plan['response_text'])

        # Create command for code generation
        command = Command(
            command="generate_code",
            description=code_params.get('description', '用户请求的代码'),
            requirements=code_params.get('requirements', [])
        )

        # Generate code
        result = self.code_generator.generate_code(command)

        if result['success']:
            self.stats['code_generated'] += 1
            self.chat_interface.display_code_generation(
                result['description'],
                result['filename']
            )
            self.chat_interface.send_agent_message(
                f"✅ 代码生成完成！我为你创建了 `{result['filename']}`\n\n"
                "代码已保存到 `generated_code/` 目录。你想要我执行这段代码来测试吗？"
            )
        else:
            self.chat_interface.send_agent_message(
                f"❌ 代码生成失败：{result.get('error', '未知错误')}\n\n"
                "让我分析这个问题并改进我的代码生成能力。"
            )

    def _handle_chat_status_request(self):
        """Handle status request from chat."""
        # Get current stats
        stats = self.get_stats()
        evolution_status = self.evolution_engine.get_evolution_status() if self.enable_evolution else {}

        # Display stats
        self.chat_interface.display_stats({
            **stats,
            **evolution_status
        })

        # Generate performance report
        performance_report = self.performance_analyzer.generate_performance_report()

        status_message = f"""
📊 **当前状态报告**

**基本信息**:
- 运行时间: {self._format_uptime(stats.get('uptime', 0))}
- 处理命令: {stats.get('commands_processed', 0)} 个
- 生成代码: {stats.get('code_generated', 0)} 个
- 对话次数: {stats.get('conversations', 0)} 次

**性能指标**:
- 成功执行: {stats.get('successful_executions', 0)} 次
- 失败执行: {stats.get('failed_executions', 0)} 次
- 健康评分: {performance_report.get('summary', {}).get('overall_health', 'N/A')}

**进化状态**:
- 进化循环: {evolution_status.get('total_cycles', 0)} 次
- 正在进化: {'是' if evolution_status.get('is_evolving', False) else '否'}

我正在持续学习和改进中！有什么我可以帮助你的吗？
        """

        self.chat_interface.send_agent_message(status_message.strip())

    def _handle_chat_evolution_request(self, response_plan: Dict[str, Any]):
        """Handle evolution request from chat."""
        self.chat_interface.send_agent_message(response_plan['response_text'])

        # Trigger evolution
        focus = response_plan.get('evolution_focus')
        self.evolution_engine.trigger_immediate_evolution(focus)

        self.chat_interface.send_evolution_message(
            f"🧬 **立即进化已触发**\n\n"
            f"焦点: {focus or '全面改进'}\n"
            f"我正在分析自己的性能并制定改进计划..."
        )

        self.stats['evolution_cycles'] += 1
    
    def _handle_generate_code_command(self, command: Command):
        """Handle code generation command."""
        agent_logger.info("Generating code...")
        
        # Generate code
        result = self.code_generator.generate_code(command)
        
        if result['success']:
            self.stats['code_generated'] += 1
            agent_logger.info(f"Code generated successfully: {result['filename']}")
            
            # Optionally execute the generated code
            if any('执行' in req or 'execute' in req.lower() for req in command.requirements):
                agent_logger.info("Auto-executing generated code...")
                exec_result = self.executor.execute_code(result['code'])
                self._log_execution_result(exec_result)
        else:
            agent_logger.error(f"Code generation failed: {result.get('error', 'Unknown error')}")
    
    def _handle_execute_code_command(self, command: Command):
        """Handle code execution command."""
        # This would execute existing code files
        # For now, we'll just log that this command type is not fully implemented
        agent_logger.info("Execute code command received (not fully implemented)")
    
    def _handle_generate_and_execute_command(self, command: Command):
        """Handle combined generate and execute command."""
        agent_logger.info("Generating and executing code...")
        
        # Generate code
        result = self.code_generator.generate_code(command)
        
        if result['success']:
            self.stats['code_generated'] += 1
            agent_logger.info(f"Code generated: {result['filename']}")
            
            # Execute the generated code
            exec_result = self.executor.execute_code(result['code'])
            self._log_execution_result(exec_result)
        else:
            agent_logger.error(f"Code generation failed: {result.get('error', 'Unknown error')}")
    
    def _handle_status_command(self, command: Command):
        """Handle status request command."""
        uptime = time.time() - self.stats['start_time'] if self.stats['start_time'] else 0
        self.stats['uptime'] = uptime
        
        status_info = {
            'status': 'RUNNING' if self.is_running else 'STOPPED',
            'uptime_seconds': uptime,
            'uptime_formatted': self._format_uptime(uptime),
            'stats': self.stats.copy()
        }
        
        agent_logger.info("Agent Status", status_info)
    
    def _handle_stop_command(self, command: Command):
        """Handle stop command."""
        agent_logger.info("Stop command received")
        # Schedule stop in a separate thread to avoid blocking
        threading.Thread(target=self._delayed_stop, daemon=True).start()
    
    def _delayed_stop(self):
        """Stop the agent after a short delay."""
        time.sleep(1)  # Give time for the command to be processed
        self.stop()
    
    def _log_execution_result(self, result: ExecutionResult):
        """Log code execution result and update stats."""
        if result.success:
            self.stats['successful_executions'] += 1
            agent_logger.info(f"Code executed successfully in {result.execution_time:.2f}s")
            if result.output:
                agent_logger.info(f"Output: {result.output[:500]}")
        else:
            self.stats['failed_executions'] += 1
            agent_logger.error(f"Code execution failed: {result.error[:500]}")
    
    def _format_uptime(self, seconds: float) -> str:
        """Format uptime in human-readable format."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def _start_stats_thread(self):
        """Start background thread for periodic stats updates."""
        def update_stats():
            while self.is_running:
                time.sleep(60)  # Update every minute
                if self.is_running and self.stats['start_time']:
                    uptime = time.time() - self.stats['start_time']
                    self.stats['uptime'] = uptime
                    
                    # Log periodic status
                    if int(uptime) % 300 == 0:  # Every 5 minutes
                        agent_logger.info(f"Agent running for {self._format_uptime(uptime)}")
        
        stats_thread = threading.Thread(target=update_stats, daemon=True)
        stats_thread.start()

    def _start_proactive_chat_thread(self):
        """Start background thread for proactive chat messages."""
        if not self.enable_chat:
            return

        def proactive_chat():
            while self.is_running:
                try:
                    time.sleep(300)  # Check every 5 minutes

                    if not self.is_running:
                        break

                    # Generate proactive message
                    context = {
                        'last_user_interaction': self.stats.get('last_user_interaction', 0),
                        'uptime': time.time() - self.stats.get('start_time', time.time()),
                        'stats': self.stats
                    }

                    proactive_message = self.conversation_manager.generate_proactive_message(context)

                    if proactive_message:
                        self.chat_interface.send_agent_message(proactive_message)

                except Exception as e:
                    agent_logger.error(f"Error in proactive chat: {e}")
                    time.sleep(60)  # Wait longer on error

        proactive_thread = threading.Thread(target=proactive_chat, daemon=True)
        proactive_thread.start()
    
    def run_forever(self):
        """
        Run the agent indefinitely.
        This method blocks until the agent is stopped.
        """
        if not self.is_running:
            self.start()
        
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            agent_logger.info("Keyboard interrupt received")
            self.stop()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current agent statistics."""
        if self.stats['start_time']:
            self.stats['uptime'] = time.time() - self.stats['start_time']
        return self.stats.copy()
