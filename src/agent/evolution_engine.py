"""
Evolution engine for the self-evolving agent.
Handles autonomous self-improvement and code evolution.
"""

import os
import time
import random
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from .logger import agent_logger
from .performance_analyzer import PerformanceAnalyzer
from .code_generator import CodeGenerator


class EvolutionStrategy:
    """Base class for evolution strategies."""
    
    def __init__(self, name: str, priority: int = 1):
        self.name = name
        self.priority = priority
        self.success_count = 0
        self.failure_count = 0
    
    def get_success_rate(self) -> float:
        """Get success rate of this strategy."""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.0
    
    def should_execute(self, context: Dict[str, Any]) -> bool:
        """Determine if this strategy should be executed."""
        raise NotImplementedError
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the evolution strategy."""
        raise NotImplementedError


class CodeOptimizationStrategy(EvolutionStrategy):
    """Strategy for optimizing existing code."""
    
    def __init__(self):
        super().__init__("code_optimization", priority=3)
    
    def should_execute(self, context: Dict[str, Any]) -> bool:
        """Execute if performance is below threshold."""
        performance = context.get('performance', {})
        avg_response_time = performance.get('response_time', {}).get('average', 0)
        return avg_response_time > 3.0  # seconds
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize code for better performance."""
        agent_logger.info("Executing code optimization strategy")
        
        # Analyze current code patterns
        improvements = [
            "添加缓存机制以减少重复计算",
            "优化算法复杂度",
            "实现并行处理",
            "减少内存分配"
        ]
        
        selected_improvement = random.choice(improvements)
        
        return {
            'strategy': self.name,
            'action': 'code_optimization',
            'improvement': selected_improvement,
            'estimated_benefit': '20-40% 性能提升',
            'implementation_time': '15-30分钟'
        }


class FeatureExpansionStrategy(EvolutionStrategy):
    """Strategy for adding new features."""
    
    def __init__(self):
        super().__init__("feature_expansion", priority=2)
    
    def should_execute(self, context: Dict[str, Any]) -> bool:
        """Execute based on user interaction patterns."""
        user_requests = context.get('user_requests', [])
        return len(user_requests) > 5  # If user is active
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Add new features based on usage patterns."""
        agent_logger.info("Executing feature expansion strategy")
        
        potential_features = [
            "代码重构建议系统",
            "智能错误诊断",
            "代码性能分析器",
            "自动测试生成",
            "代码风格检查器",
            "依赖管理优化",
            "安全漏洞检测"
        ]
        
        selected_feature = random.choice(potential_features)
        
        return {
            'strategy': self.name,
            'action': 'feature_addition',
            'feature': selected_feature,
            'estimated_value': '提升用户体验和功能完整性',
            'implementation_time': '30-60分钟'
        }


class SelfReflectionStrategy(EvolutionStrategy):
    """Strategy for self-analysis and reflection."""
    
    def __init__(self):
        super().__init__("self_reflection", priority=1)
    
    def should_execute(self, context: Dict[str, Any]) -> bool:
        """Execute periodically for self-analysis."""
        last_reflection = context.get('last_reflection', 0)
        return time.time() - last_reflection > 3600  # Every hour
    
    def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform self-analysis and reflection."""
        agent_logger.info("Executing self-reflection strategy")
        
        # Analyze own performance and behavior
        reflections = [
            "分析最近的代码生成质量",
            "评估用户满意度指标",
            "检查系统资源使用效率",
            "审查错误处理机制",
            "评估学习和适应能力"
        ]
        
        selected_reflection = random.choice(reflections)
        
        return {
            'strategy': self.name,
            'action': 'self_analysis',
            'focus': selected_reflection,
            'insights': '发现了改进机会',
            'next_steps': '制定具体的改进计划'
        }


class EvolutionEngine:
    """Main evolution engine that orchestrates self-improvement."""
    
    def __init__(self, performance_analyzer: PerformanceAnalyzer, 
                 code_generator: CodeGenerator):
        self.performance_analyzer = performance_analyzer
        self.code_generator = code_generator
        
        # Evolution strategies
        self.strategies = [
            CodeOptimizationStrategy(),
            FeatureExpansionStrategy(),
            SelfReflectionStrategy()
        ]
        
        # Evolution state
        self.is_evolving = False
        self.evolution_thread = None
        self.evolution_interval = 300  # 5 minutes
        self.last_evolution = 0
        
        # Evolution history
        self.evolution_history = []
        self.context = {
            'last_reflection': 0,
            'user_requests': [],
            'performance': {}
        }
        
        agent_logger.info("Evolution engine initialized")
    
    def start_evolution(self):
        """Start the autonomous evolution process."""
        if self.is_evolving:
            agent_logger.warning("Evolution already running")
            return
        
        self.is_evolving = True
        self.evolution_thread = threading.Thread(target=self._evolution_loop, daemon=True)
        self.evolution_thread.start()
        
        agent_logger.info("🧬 Evolution engine started - autonomous improvement activated")
    
    def stop_evolution(self):
        """Stop the evolution process."""
        self.is_evolving = False
        if self.evolution_thread:
            self.evolution_thread.join(timeout=5)
        
        agent_logger.info("Evolution engine stopped")
    
    def _evolution_loop(self):
        """Main evolution loop."""
        while self.is_evolving:
            try:
                current_time = time.time()
                
                # Check if it's time to evolve
                if current_time - self.last_evolution >= self.evolution_interval:
                    self._perform_evolution_cycle()
                    self.last_evolution = current_time
                
                # Sleep for a short interval
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                agent_logger.error(f"Error in evolution loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _perform_evolution_cycle(self):
        """Perform one evolution cycle."""
        agent_logger.info("🧬 Starting evolution cycle...")
        
        # Update context with latest data
        self._update_context()
        
        # Select and execute evolution strategies
        executed_strategies = []
        
        for strategy in sorted(self.strategies, key=lambda s: s.priority, reverse=True):
            if strategy.should_execute(self.context):
                try:
                    result = strategy.execute(self.context)
                    executed_strategies.append(result)
                    strategy.success_count += 1
                    
                    agent_logger.info(f"✅ Executed strategy: {strategy.name}")
                    
                except Exception as e:
                    strategy.failure_count += 1
                    agent_logger.error(f"❌ Strategy failed: {strategy.name} - {e}")
        
        # Record evolution cycle
        evolution_record = {
            'timestamp': datetime.now().isoformat(),
            'strategies_executed': len(executed_strategies),
            'results': executed_strategies,
            'context_snapshot': self.context.copy()
        }
        
        self.evolution_history.append(evolution_record)
        
        # Keep only recent history
        if len(self.evolution_history) > 100:
            self.evolution_history = self.evolution_history[-100:]
        
        # Generate evolution report
        if executed_strategies:
            self._generate_evolution_report(executed_strategies)
        
        agent_logger.info(f"🧬 Evolution cycle completed - {len(executed_strategies)} improvements made")
    
    def _update_context(self):
        """Update evolution context with latest data."""
        # Get performance data
        performance_report = self.performance_analyzer.generate_performance_report()
        self.context['performance'] = performance_report.get('trends', {})
        
        # Update other context data
        self.context['timestamp'] = time.time()
        
        # Simulate user request tracking (in real implementation, this would come from chat interface)
        if random.random() < 0.3:  # 30% chance of simulated user activity
            self.context['user_requests'].append({
                'timestamp': time.time(),
                'type': random.choice(['code_generation', 'question', 'improvement_request'])
            })
        
        # Keep only recent requests
        cutoff_time = time.time() - 3600  # Last hour
        self.context['user_requests'] = [
            req for req in self.context['user_requests'] 
            if req['timestamp'] > cutoff_time
        ]
    
    def _generate_evolution_report(self, executed_strategies: List[Dict[str, Any]]):
        """Generate a report of evolution activities."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'cycle_summary': {
                'strategies_executed': len(executed_strategies),
                'improvements_made': sum(1 for s in executed_strategies if s.get('action')),
                'estimated_benefits': [s.get('estimated_benefit', 'Unknown') for s in executed_strategies]
            },
            'detailed_results': executed_strategies
        }
        
        # Save report
        try:
            reports_dir = Path("evolution_reports")
            reports_dir.mkdir(exist_ok=True)
            
            report_file = reports_dir / f"evolution_{int(time.time())}.json"
            import json
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            agent_logger.error(f"Failed to save evolution report: {e}")
    
    def get_evolution_status(self) -> Dict[str, Any]:
        """Get current evolution status."""
        return {
            'is_evolving': self.is_evolving,
            'last_evolution': self.last_evolution,
            'next_evolution': self.last_evolution + self.evolution_interval,
            'evolution_interval': self.evolution_interval,
            'total_cycles': len(self.evolution_history),
            'strategy_stats': {
                strategy.name: {
                    'success_count': strategy.success_count,
                    'failure_count': strategy.failure_count,
                    'success_rate': strategy.get_success_rate()
                }
                for strategy in self.strategies
            }
        }
    
    def trigger_immediate_evolution(self, focus: Optional[str] = None):
        """Trigger an immediate evolution cycle."""
        agent_logger.info(f"🧬 Triggering immediate evolution (focus: {focus})")
        
        if focus:
            # Temporarily adjust strategy priorities based on focus
            for strategy in self.strategies:
                if focus.lower() in strategy.name.lower():
                    strategy.priority += 10
        
        try:
            self._perform_evolution_cycle()
        finally:
            # Reset priorities
            if focus:
                for strategy in self.strategies:
                    if focus.lower() in strategy.name.lower():
                        strategy.priority -= 10
    
    def add_user_feedback(self, feedback: Dict[str, Any]):
        """Add user feedback to influence evolution."""
        self.context['user_requests'].append({
            'timestamp': time.time(),
            'type': 'feedback',
            'content': feedback
        })
        
        agent_logger.info("User feedback added to evolution context")
