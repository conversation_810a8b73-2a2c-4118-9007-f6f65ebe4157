"""
Code generator for the self-coding agent.
Generates Python code based on command descriptions and requirements.
"""

import re
import ast
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
from .logger import agent_logger
from .command_receiver import Command


class CodeTemplate:
    """Code templates for common programming patterns."""
    
    FUNCTION_TEMPLATE = '''def {function_name}({parameters}):
    """
    {description}
    
    Args:
        {args_doc}
    
    Returns:
        {return_doc}
    """
    {body}
'''

    CLASS_TEMPLATE = '''class {class_name}:
    """
    {description}
    """
    
    def __init__(self{init_params}):
        {init_body}
    
    {methods}
'''

    SCRIPT_TEMPLATE = '''#!/usr/bin/env python3
"""
{description}
"""

{imports}

{main_code}

if __name__ == "__main__":
    {main_execution}
'''


class CodeGenerator:
    """Generates Python code based on natural language descriptions."""
    
    def __init__(self, output_dir: str = "generated_code"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.templates = CodeTemplate()
        
        # Simple pattern matching for common programming tasks
        self.patterns = {
            r'斐波那契|fibonacci': self._generate_fibonacci,
            r'排序|sort': self._generate_sort,
            r'计算器|calculator': self._generate_calculator,
            r'质数|prime': self._generate_prime,
            r'阶乘|factorial': self._generate_factorial,
            r'类|class': self._generate_class,
            r'函数|function': self._generate_function,
        }
    
    def generate_code(self, command: Command) -> Dict[str, Any]:
        """
        Generate code based on command description and requirements.
        
        Args:
            command: Command object containing description and requirements
            
        Returns:
            Dict containing generated code, filename, and metadata
        """
        agent_logger.info(f"Generating code for: {command.description}")
        
        try:
            # Analyze the command to determine what type of code to generate
            code_info = self._analyze_command(command)
            
            # Generate the actual code
            generated_code = self._generate_code_from_info(code_info, command)
            
            # Create filename
            filename = self._create_filename(command.description)
            
            # Save the generated code
            file_path = self.output_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(generated_code)
            
            result = {
                'code': generated_code,
                'filename': filename,
                'file_path': str(file_path),
                'description': command.description,
                'requirements': command.requirements,
                'timestamp': time.time(),
                'success': True
            }
            
            agent_logger.log_code_generated(generated_code, command.description)
            return result
            
        except Exception as e:
            agent_logger.error(f"Code generation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'description': command.description
            }
    
    def _analyze_command(self, command: Command) -> Dict[str, Any]:
        """Analyze command to determine code generation strategy."""
        description = command.description.lower()
        requirements = [req.lower() for req in command.requirements]
        
        # Check for pattern matches
        for pattern, generator_func in self.patterns.items():
            if re.search(pattern, description):
                return {
                    'type': 'pattern_match',
                    'pattern': pattern,
                    'generator': generator_func,
                    'description': description,
                    'requirements': requirements
                }
        
        # Default to generic function generation
        return {
            'type': 'generic',
            'generator': self._generate_generic_function,
            'description': description,
            'requirements': requirements
        }
    
    def _generate_code_from_info(self, code_info: Dict[str, Any], command: Command) -> str:
        """Generate code based on analysis info."""
        generator_func = code_info['generator']
        return generator_func(command)
    
    def _create_filename(self, description: str) -> str:
        """Create a valid filename from description."""
        # Remove special characters and replace spaces with underscores
        filename = re.sub(r'[^\w\s-]', '', description)
        filename = re.sub(r'[-\s]+', '_', filename)
        filename = filename.lower().strip('_')
        
        # Add timestamp to avoid conflicts
        timestamp = int(time.time())
        return f"{filename}_{timestamp}.py"
    
    def _generate_fibonacci(self, command: Command) -> str:
        """Generate Fibonacci sequence code."""
        return '''def fibonacci(n):
    """
    计算第n个斐波那契数
    
    Args:
        n (int): 要计算的斐波那契数的位置
    
    Returns:
        int: 第n个斐波那契数
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n - 1) + fibonacci(n - 2)


def fibonacci_iterative(n):
    """
    使用迭代方法计算斐波那契数（更高效）
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b


# 测试代码
if __name__ == "__main__":
    for i in range(10):
        print(f"fibonacci({i}) = {fibonacci(i)}")
'''
    
    def _generate_sort(self, command: Command) -> str:
        """Generate sorting algorithm code."""
        return '''def bubble_sort(arr):
    """
    冒泡排序算法
    
    Args:
        arr (list): 要排序的列表
    
    Returns:
        list: 排序后的列表
    """
    arr = arr.copy()  # 不修改原列表
    n = len(arr)
    
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    
    return arr


def quick_sort(arr):
    """
    快速排序算法
    """
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)


# 测试代码
if __name__ == "__main__":
    test_data = [64, 34, 25, 12, 22, 11, 90]
    print(f"原始数据: {test_data}")
    print(f"冒泡排序: {bubble_sort(test_data)}")
    print(f"快速排序: {quick_sort(test_data)}")
'''
    
    def _generate_calculator(self, command: Command) -> str:
        """Generate calculator code."""
        return '''class Calculator:
    """
    简单计算器类
    """
    
    def add(self, a, b):
        """加法"""
        return a + b
    
    def subtract(self, a, b):
        """减法"""
        return a - b
    
    def multiply(self, a, b):
        """乘法"""
        return a * b
    
    def divide(self, a, b):
        """除法"""
        if b == 0:
            raise ValueError("除数不能为零")
        return a / b
    
    def power(self, a, b):
        """幂运算"""
        return a ** b
    
    def calculate(self, expression):
        """
        计算数学表达式
        注意：这是一个简单实现，仅支持基本运算
        """
        try:
            # 安全的表达式求值
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                raise ValueError("表达式包含不允许的字符")
            
            return eval(expression)
        except Exception as e:
            raise ValueError(f"表达式计算错误: {e}")


# 测试代码
if __name__ == "__main__":
    calc = Calculator()
    
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(f"2 ** 8 = {calc.power(2, 8)}")
    
    print(f"(5 + 3) * 2 = {calc.calculate('(5 + 3) * 2')}")
'''
    
    def _generate_prime(self, command: Command) -> str:
        """Generate prime number related code."""
        return '''def is_prime(n):
    """
    判断一个数是否为质数
    
    Args:
        n (int): 要判断的数
    
    Returns:
        bool: 如果是质数返回True，否则返回False
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    
    for i in range(3, int(n ** 0.5) + 1, 2):
        if n % i == 0:
            return False
    return True


def generate_primes(limit):
    """
    生成小于limit的所有质数
    
    Args:
        limit (int): 上限
    
    Returns:
        list: 质数列表
    """
    primes = []
    for num in range(2, limit):
        if is_prime(num):
            primes.append(num)
    return primes


def sieve_of_eratosthenes(limit):
    """
    使用埃拉托斯特尼筛法生成质数
    """
    sieve = [True] * limit
    sieve[0] = sieve[1] = False
    
    for i in range(2, int(limit ** 0.5) + 1):
        if sieve[i]:
            for j in range(i * i, limit, i):
                sieve[j] = False
    
    return [i for i in range(limit) if sieve[i]]


# 测试代码
if __name__ == "__main__":
    print("前20个质数:")
    primes = generate_primes(100)
    print(primes[:20])
    
    print("\\n使用筛法生成前20个质数:")
    sieve_primes = sieve_of_eratosthenes(100)
    print(sieve_primes[:20])
'''
    
    def _generate_factorial(self, command: Command) -> str:
        """Generate factorial calculation code."""
        return '''def factorial(n):
    """
    计算n的阶乘
    
    Args:
        n (int): 要计算阶乘的数
    
    Returns:
        int: n的阶乘
    """
    if n < 0:
        raise ValueError("阶乘不能计算负数")
    if n == 0 or n == 1:
        return 1
    
    result = 1
    for i in range(2, n + 1):
        result *= i
    return result


def factorial_recursive(n):
    """
    递归方式计算阶乘
    """
    if n < 0:
        raise ValueError("阶乘不能计算负数")
    if n == 0 or n == 1:
        return 1
    return n * factorial_recursive(n - 1)


# 测试代码
if __name__ == "__main__":
    for i in range(10):
        print(f"{i}! = {factorial(i)}")
'''
    
    def _generate_class(self, command: Command) -> str:
        """Generate a generic class."""
        class_name = "MyClass"
        
        # Try to extract class name from requirements
        for req in command.requirements:
            if "类名" in req or "class" in req.lower():
                # Extract class name
                words = req.split()
                for word in words:
                    if word.isalpha() and word not in ["类名", "class", "为", "是"]:
                        class_name = word.capitalize()
                        break
        
        return f'''class {class_name}:
    """
    {command.description}
    """
    
    def __init__(self):
        """初始化方法"""
        pass
    
    def method_example(self):
        """示例方法"""
        return "Hello from {class_name}"


# 测试代码
if __name__ == "__main__":
    obj = {class_name}()
    print(obj.method_example())
'''
    
    def _generate_function(self, command: Command) -> str:
        """Generate a generic function."""
        return self._generate_generic_function(command)
    
    def _generate_generic_function(self, command: Command) -> str:
        """Generate a generic function based on description."""
        function_name = "my_function"
        
        # Try to extract function name from requirements
        for req in command.requirements:
            if "函数名" in req or "function" in req.lower():
                words = req.split()
                for word in words:
                    if word.isalpha() and word not in ["函数名", "function", "为", "是"]:
                        function_name = word.lower()
                        break
        
        return f'''def {function_name}():
    """
    {command.description}
    
    根据需求:
    {chr(10).join(f"- {req}" for req in command.requirements)}
    """
    # TODO: 实现具体功能
    print("函数 {function_name} 被调用")
    return "功能待实现"


# 测试代码
if __name__ == "__main__":
    result = {function_name}()
    print(f"结果: {{result}}")
'''
