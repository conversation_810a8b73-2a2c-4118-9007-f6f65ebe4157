"""
Performance analyzer for the self-evolving agent.
Monitors and analyzes agent performance for self-improvement.
"""

import time
import json
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from .logger import agent_logger


@dataclass
class PerformanceMetric:
    """Represents a performance metric."""
    timestamp: float
    metric_name: str
    value: float
    context: Dict[str, Any]


@dataclass
class CodeQualityMetric:
    """Represents code quality metrics."""
    lines_of_code: int
    complexity_score: float
    readability_score: float
    test_coverage: float
    execution_time: float
    memory_usage: float
    error_rate: float


class PerformanceAnalyzer:
    """Analyzes agent performance and identifies improvement opportunities."""
    
    def __init__(self, data_dir: str = "performance_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Performance tracking
        self.metrics: List[PerformanceMetric] = []
        self.code_quality_history: List[CodeQualityMetric] = []
        
        # Analysis thresholds
        self.thresholds = {
            'response_time': 5.0,  # seconds
            'success_rate': 0.8,   # 80%
            'memory_usage': 100,   # MB
            'error_rate': 0.1,     # 10%
        }
        
        # Load historical data
        self._load_historical_data()
    
    def record_metric(self, name: str, value: float, context: Dict[str, Any] = None):
        """Record a performance metric."""
        metric = PerformanceMetric(
            timestamp=time.time(),
            metric_name=name,
            value=value,
            context=context or {}
        )
        
        self.metrics.append(metric)
        agent_logger.debug(f"Recorded metric: {name} = {value}")
        
        # Save to disk periodically
        if len(self.metrics) % 10 == 0:
            self._save_metrics()
    
    def record_code_quality(self, quality_metric: CodeQualityMetric):
        """Record code quality metrics."""
        self.code_quality_history.append(quality_metric)
        agent_logger.debug(f"Recorded code quality: {quality_metric}")
    
    def analyze_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze performance trends over the specified time period."""
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.metrics if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {"status": "insufficient_data"}
        
        # Group metrics by name
        metric_groups = {}
        for metric in recent_metrics:
            if metric.metric_name not in metric_groups:
                metric_groups[metric.metric_name] = []
            metric_groups[metric.metric_name].append(metric.value)
        
        # Calculate trends
        trends = {}
        for name, values in metric_groups.items():
            if len(values) >= 2:
                trends[name] = {
                    'average': statistics.mean(values),
                    'median': statistics.median(values),
                    'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
                    'min': min(values),
                    'max': max(values),
                    'count': len(values),
                    'trend': self._calculate_trend(values)
                }
        
        return {
            'status': 'success',
            'period_hours': hours,
            'trends': trends,
            'total_metrics': len(recent_metrics)
        }
    
    def identify_improvement_opportunities(self) -> List[Dict[str, Any]]:
        """Identify areas where the agent can improve."""
        opportunities = []
        
        # Analyze recent performance
        trends = self.analyze_performance_trends(24)
        
        if trends['status'] != 'success':
            return opportunities
        
        # Check each metric against thresholds
        for metric_name, trend_data in trends['trends'].items():
            avg_value = trend_data['average']
            
            if metric_name == 'response_time' and avg_value > self.thresholds['response_time']:
                opportunities.append({
                    'type': 'performance',
                    'issue': 'slow_response_time',
                    'current_value': avg_value,
                    'threshold': self.thresholds['response_time'],
                    'severity': 'high' if avg_value > self.thresholds['response_time'] * 2 else 'medium',
                    'suggestion': 'Optimize code generation algorithms or implement caching'
                })
            
            elif metric_name == 'success_rate' and avg_value < self.thresholds['success_rate']:
                opportunities.append({
                    'type': 'reliability',
                    'issue': 'low_success_rate',
                    'current_value': avg_value,
                    'threshold': self.thresholds['success_rate'],
                    'severity': 'high',
                    'suggestion': 'Improve error handling and code validation'
                })
            
            elif metric_name == 'memory_usage' and avg_value > self.thresholds['memory_usage']:
                opportunities.append({
                    'type': 'resource',
                    'issue': 'high_memory_usage',
                    'current_value': avg_value,
                    'threshold': self.thresholds['memory_usage'],
                    'severity': 'medium',
                    'suggestion': 'Implement memory optimization and garbage collection'
                })
        
        # Analyze code quality trends
        if len(self.code_quality_history) >= 5:
            recent_quality = self.code_quality_history[-5:]
            avg_complexity = statistics.mean([q.complexity_score for q in recent_quality])
            avg_readability = statistics.mean([q.readability_score for q in recent_quality])
            
            if avg_complexity > 10:  # Arbitrary threshold
                opportunities.append({
                    'type': 'code_quality',
                    'issue': 'high_complexity',
                    'current_value': avg_complexity,
                    'threshold': 10,
                    'severity': 'medium',
                    'suggestion': 'Refactor complex functions and improve code structure'
                })
            
            if avg_readability < 7:  # Arbitrary threshold (1-10 scale)
                opportunities.append({
                    'type': 'code_quality',
                    'issue': 'low_readability',
                    'current_value': avg_readability,
                    'threshold': 7,
                    'severity': 'low',
                    'suggestion': 'Improve code documentation and naming conventions'
                })
        
        return opportunities
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate a comprehensive performance report."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {},
            'trends': {},
            'improvements': [],
            'recommendations': []
        }
        
        # Get recent trends
        trends = self.analyze_performance_trends(24)
        report['trends'] = trends
        
        # Get improvement opportunities
        opportunities = self.identify_improvement_opportunities()
        report['improvements'] = opportunities
        
        # Generate summary
        if trends['status'] == 'success':
            total_metrics = trends['total_metrics']
            report['summary'] = {
                'total_metrics_24h': total_metrics,
                'metrics_tracked': len(trends['trends']),
                'improvement_opportunities': len(opportunities),
                'overall_health': self._calculate_health_score(trends, opportunities)
            }
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(opportunities)
        
        return report
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate if values are trending up, down, or stable."""
        if len(values) < 3:
            return 'insufficient_data'
        
        # Simple linear trend calculation
        n = len(values)
        x = list(range(n))
        y = values
        
        # Calculate slope
        x_mean = statistics.mean(x)
        y_mean = statistics.mean(y)
        
        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 'stable'
        
        slope = numerator / denominator
        
        if slope > 0.1:
            return 'increasing'
        elif slope < -0.1:
            return 'decreasing'
        else:
            return 'stable'
    
    def _calculate_health_score(self, trends: Dict[str, Any], opportunities: List[Dict[str, Any]]) -> float:
        """Calculate overall agent health score (0-100)."""
        base_score = 100.0
        
        # Deduct points for improvement opportunities
        for opp in opportunities:
            if opp['severity'] == 'high':
                base_score -= 20
            elif opp['severity'] == 'medium':
                base_score -= 10
            else:
                base_score -= 5
        
        return max(0.0, min(100.0, base_score))
    
    def _generate_recommendations(self, opportunities: List[Dict[str, Any]]) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Group opportunities by type
        by_type = {}
        for opp in opportunities:
            opp_type = opp['type']
            if opp_type not in by_type:
                by_type[opp_type] = []
            by_type[opp_type].append(opp)
        
        # Generate recommendations by type
        if 'performance' in by_type:
            recommendations.append("优化代码生成算法以提高响应速度")
            recommendations.append("实现智能缓存机制减少重复计算")
        
        if 'reliability' in by_type:
            recommendations.append("增强错误处理和恢复机制")
            recommendations.append("实现更严格的代码验证流程")
        
        if 'code_quality' in by_type:
            recommendations.append("重构复杂函数以降低复杂度")
            recommendations.append("改进代码文档和注释质量")
        
        if 'resource' in by_type:
            recommendations.append("优化内存使用和垃圾回收")
            recommendations.append("实现资源监控和限制机制")
        
        return recommendations
    
    def _save_metrics(self):
        """Save metrics to disk."""
        try:
            metrics_file = self.data_dir / "metrics.json"
            metrics_data = [asdict(m) for m in self.metrics[-100:]]  # Keep last 100
            
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
                
        except Exception as e:
            agent_logger.error(f"Failed to save metrics: {e}")
    
    def _load_historical_data(self):
        """Load historical performance data."""
        try:
            metrics_file = self.data_dir / "metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    metrics_data = json.load(f)
                
                self.metrics = [
                    PerformanceMetric(**data) for data in metrics_data
                ]
                
                agent_logger.info(f"Loaded {len(self.metrics)} historical metrics")
                
        except Exception as e:
            agent_logger.error(f"Failed to load historical data: {e}")
