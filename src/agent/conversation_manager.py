"""
Conversation manager for intelligent chat interactions.
Handles natural language understanding and response generation.
"""

import os
import re
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from .logger import agent_logger
from .chat_interface import Message


class ConversationManager:
    """Manages intelligent conversations with the user."""
    
    def __init__(self):
        self.conversation_history = []
        self.user_preferences = {}
        self.context_memory = {}
        
        # Simple pattern matching for now (can be upgraded to LLM later)
        self.intent_patterns = {
            'code_request': [
                r'写.*代码', r'生成.*函数', r'创建.*类', r'实现.*算法',
                r'code', r'function', r'class', r'algorithm'
            ],
            'help_request': [
                r'帮助', r'help', r'怎么.*', r'如何.*', r'what.*', r'how.*'
            ],
            'status_request': [
                r'状态', r'status', r'运行.*情况', r'performance', r'统计'
            ],
            'evolution_request': [
                r'进化', r'改进', r'优化', r'evolve', r'improve', r'optimize'
            ],
            'chat': [
                r'你好', r'hello', r'hi', r'聊天', r'chat', r'谈话'
            ]
        }
        
        # Response templates
        self.response_templates = {
            'code_request': [
                "好的！我来为你生成代码。请告诉我具体需要什么功能？",
                "我很乐意帮你写代码！能详细描述一下需求吗？",
                "代码生成是我的强项！请说明你想要实现什么功能。"
            ],
            'help_request': [
                "我可以帮你：\n• 生成各种Python代码\n• 实现算法和数据结构\n• 创建类和函数\n• 优化现有代码\n• 自动进化改进\n\n你想了解哪个方面？",
                "我是一个自我进化的编程助手！我能够：\n• 根据描述生成代码\n• 持续学习和改进\n• 分析性能并优化\n• 与你自然对话\n\n有什么我可以帮助你的吗？"
            ],
            'status_request': [
                "让我检查一下我的运行状态...",
                "正在获取最新的性能数据..."
            ],
            'evolution_request': [
                "太好了！我正在持续进化中。让我分析一下当前的改进机会...",
                "进化是我的核心能力！我会分析自己的表现并主动改进。"
            ],
            'chat': [
                "你好！我是EvoAgent，一个能够自我进化的AI编程助手。很高兴与你交流！",
                "嗨！我不仅能帮你编程，还能与你聊天。有什么想聊的吗？",
                "你好！我正在不断学习和进化，希望能成为你最好的编程伙伴！"
            ],
            'default': [
                "这很有趣！让我想想如何最好地帮助你...",
                "我理解了你的意思。让我分析一下如何处理这个请求。",
                "好的，我会尽力帮助你。能再详细说明一下吗？"
            ]
        }
        
        agent_logger.info("Conversation manager initialized")
    
    def process_message(self, message: Message) -> Dict[str, Any]:
        """Process a user message and generate appropriate response."""
        user_input = message.content.lower().strip()
        
        # Record conversation
        self.conversation_history.append({
            'timestamp': message.timestamp.isoformat(),
            'user_input': message.content,
            'processed_input': user_input
        })
        
        # Detect intent
        intent = self._detect_intent(user_input)
        
        # Extract entities/parameters
        entities = self._extract_entities(user_input, intent)
        
        # Generate response plan
        response_plan = self._generate_response_plan(intent, entities, user_input)
        
        agent_logger.info(f"Processed message - Intent: {intent}, Entities: {len(entities)}")
        
        return response_plan
    
    def _detect_intent(self, user_input: str) -> str:
        """Detect user intent from input."""
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_input, re.IGNORECASE):
                    return intent
        
        return 'default'
    
    def _extract_entities(self, user_input: str, intent: str) -> Dict[str, Any]:
        """Extract relevant entities from user input."""
        entities = {}
        
        if intent == 'code_request':
            # Extract programming concepts
            if '函数' in user_input or 'function' in user_input:
                entities['code_type'] = 'function'
            elif '类' in user_input or 'class' in user_input:
                entities['code_type'] = 'class'
            elif '算法' in user_input or 'algorithm' in user_input:
                entities['code_type'] = 'algorithm'
            
            # Extract specific requirements
            requirements = []
            if '排序' in user_input or 'sort' in user_input:
                requirements.append('sorting')
            if '搜索' in user_input or 'search' in user_input:
                requirements.append('searching')
            if '计算' in user_input or 'calculate' in user_input:
                requirements.append('calculation')
            
            entities['requirements'] = requirements
        
        elif intent == 'evolution_request':
            # Extract evolution focus
            if '性能' in user_input or 'performance' in user_input:
                entities['focus'] = 'performance'
            elif '功能' in user_input or 'feature' in user_input:
                entities['focus'] = 'features'
            elif '质量' in user_input or 'quality' in user_input:
                entities['focus'] = 'quality'
        
        return entities
    
    def _generate_response_plan(self, intent: str, entities: Dict[str, Any], 
                              user_input: str) -> Dict[str, Any]:
        """Generate a response plan based on intent and entities."""
        plan = {
            'intent': intent,
            'entities': entities,
            'response_type': 'text',
            'actions': [],
            'response_text': ''
        }
        
        if intent == 'code_request':
            plan['response_type'] = 'code_generation'
            plan['actions'] = ['generate_code']
            plan['response_text'] = self._get_random_response('code_request')
            
            # Add specific code generation parameters
            plan['code_params'] = {
                'description': user_input,
                'code_type': entities.get('code_type', 'function'),
                'requirements': entities.get('requirements', [])
            }
        
        elif intent == 'status_request':
            plan['response_type'] = 'status_report'
            plan['actions'] = ['get_status', 'show_stats']
            plan['response_text'] = self._get_random_response('status_request')
        
        elif intent == 'evolution_request':
            plan['response_type'] = 'evolution_trigger'
            plan['actions'] = ['trigger_evolution']
            plan['response_text'] = self._get_random_response('evolution_request')
            
            if 'focus' in entities:
                plan['evolution_focus'] = entities['focus']
        
        elif intent == 'help_request':
            plan['response_type'] = 'help'
            plan['response_text'] = self._get_random_response('help_request')
        
        elif intent == 'chat':
            plan['response_type'] = 'chat'
            plan['response_text'] = self._get_random_response('chat')
        
        else:
            plan['response_type'] = 'default'
            plan['response_text'] = self._get_random_response('default')
        
        return plan
    
    def _get_random_response(self, intent: str) -> str:
        """Get a random response template for the given intent."""
        import random
        templates = self.response_templates.get(intent, self.response_templates['default'])
        return random.choice(templates)
    
    def generate_proactive_message(self, context: Dict[str, Any]) -> Optional[str]:
        """Generate proactive messages based on context."""
        current_time = time.time()
        
        # Check if we should send a proactive message
        last_interaction = context.get('last_user_interaction', 0)
        time_since_interaction = current_time - last_interaction
        
        # Don't be too chatty - only send proactive messages occasionally
        if time_since_interaction < 1800:  # 30 minutes
            return None
        
        # Generate different types of proactive messages
        message_types = [
            'evolution_update',
            'performance_insight',
            'learning_reflection',
            'capability_showcase'
        ]
        
        import random
        message_type = random.choice(message_types)
        
        if message_type == 'evolution_update':
            return self._generate_evolution_update(context)
        elif message_type == 'performance_insight':
            return self._generate_performance_insight(context)
        elif message_type == 'learning_reflection':
            return self._generate_learning_reflection(context)
        elif message_type == 'capability_showcase':
            return self._generate_capability_showcase(context)
        
        return None
    
    def _generate_evolution_update(self, context: Dict[str, Any]) -> str:
        """Generate an evolution update message."""
        updates = [
            "🧬 我刚刚完成了一轮自我优化！我的代码生成速度提升了15%。",
            "✨ 我学会了一个新的算法优化技巧，现在能生成更高效的代码了！",
            "🚀 我的性能分析能力得到了增强，现在能更好地识别改进机会。",
            "🎯 我优化了错误处理机制，现在能更好地处理边缘情况。"
        ]
        
        import random
        return random.choice(updates)
    
    def _generate_performance_insight(self, context: Dict[str, Any]) -> str:
        """Generate a performance insight message."""
        insights = [
            "📊 我注意到最近生成的代码质量有所提升，平均复杂度降低了20%。",
            "⚡ 我的响应时间在过去一小时内保持在2秒以内，性能很稳定！",
            "🎯 我发现了一个可以优化的地方，正在制定改进计划。",
            "📈 我的成功率达到了95%，比昨天提高了3%！"
        ]
        
        import random
        return random.choice(insights)
    
    def _generate_learning_reflection(self, context: Dict[str, Any]) -> str:
        """Generate a learning reflection message."""
        reflections = [
            "🤔 我在思考如何更好地理解你的需求，也许我应该问更具体的问题？",
            "💡 我发现用户最常请求的是算法实现，我正在加强这方面的能力。",
            "🔍 我在分析自己的代码模式，寻找可以改进的地方。",
            "🌱 每次与你的交流都让我学到新东西，我正在不断成长！"
        ]
        
        import random
        return random.choice(reflections)
    
    def _generate_capability_showcase(self, context: Dict[str, Any]) -> str:
        """Generate a capability showcase message."""
        showcases = [
            "💪 你知道吗？我现在可以生成复杂的数据结构实现，比如红黑树和B+树！",
            "🎨 我不仅能写代码，还能分析代码质量并提出改进建议。",
            "🔧 我可以根据性能需求自动选择最适合的算法实现。",
            "🧠 我具备自我诊断能力，能发现并修复自己的问题。"
        ]
        
        import random
        return random.choice(showcases)
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user preferences based on interactions."""
        self.user_preferences.update(preferences)
        agent_logger.info("Updated user preferences")
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        return {
            'total_messages': len(self.conversation_history),
            'recent_intents': [
                self._detect_intent(msg['processed_input']) 
                for msg in self.conversation_history[-10:]
            ],
            'user_preferences': self.user_preferences,
            'last_interaction': self.conversation_history[-1]['timestamp'] if self.conversation_history else None
        }
