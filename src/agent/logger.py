"""
Logger system for the self-coding agent.
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional


class AgentLogger:
    """Enhanced logger for the self-coding agent."""
    
    def __init__(self, name: str = "SelfCodingAgent", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create console for rich output
        self.console = Console()
        
        # Setup logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Setup handlers
        self._setup_file_handler()
        self._setup_console_handler()
    
    def _setup_file_handler(self):
        """Setup file handler for logging to file."""
        log_file = self.log_dir / f"{self.name.lower()}_{datetime.now().strftime('%Y%m%d')}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
    
    def _setup_console_handler(self):
        """Setup rich console handler for beautiful terminal output."""
        console_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=False,
            rich_tracebacks=True
        )
        console_handler.setLevel(logging.INFO)
        
        self.logger.addHandler(console_handler)
    
    def info(self, message: str, extra_data: Optional[dict] = None):
        """Log info message."""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.info(message)
    
    def debug(self, message: str, extra_data: Optional[dict] = None):
        """Log debug message."""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.debug(message)
    
    def warning(self, message: str, extra_data: Optional[dict] = None):
        """Log warning message."""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.warning(message)
    
    def error(self, message: str, extra_data: Optional[dict] = None):
        """Log error message."""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.error(message)
    
    def critical(self, message: str, extra_data: Optional[dict] = None):
        """Log critical message."""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.critical(message)
    
    def log_command_received(self, command: dict):
        """Log when a command is received."""
        self.info("Command received", {"command": command.get("command"), "description": command.get("description")})
    
    def log_code_generated(self, code: str, description: str):
        """Log when code is generated."""
        self.info(f"Code generated for: {description}", {"code_length": len(code)})
    
    def log_code_executed(self, success: bool, output: str = "", error: str = ""):
        """Log code execution result."""
        if success:
            self.info("Code executed successfully", {"output": output[:200]})
        else:
            self.error("Code execution failed", {"error": error[:200]})
    
    def log_agent_status(self, status: str):
        """Log agent status changes."""
        self.info(f"Agent status: {status}")


# Global logger instance
agent_logger = AgentLogger()
