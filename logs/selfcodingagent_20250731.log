2025-07-31 21:28:23,838 - SelfCodingAgent - INFO - Self-coding agent initialized
2025-07-31 21:28:23,839 - SelfCodingAgent - INFO - Running in test mode
2025-07-31 21:28:23,840 - SelfCodingAgent - INFO - Agent status: STARTING
2025-07-31 21:28:23,840 - SelfCodingAgent - INFO - Starting code execution
2025-07-31 21:28:23,858 - SelfCodingAgent - INFO - Code executed successfully | Data: {'output': 'Hello, World!\n2 + 2 = 4\n'}
2025-07-31 21:28:23,859 - SelfCodingAgent - INFO - Execution environment test passed
2025-07-31 21:28:23,859 - SelfCodingAgent - INFO - Example command file created: commands/example_command.json
2025-07-31 21:28:23,860 - SelfCodingAgent - INFO - Starting command receiver, monitoring: commands
2025-07-31 21:28:23,862 - SelfCodingAgent - DEBUG - Processing command file: commands/example_command.json
2025-07-31 21:28:23,862 - SelfCodingAgent - INFO - Command received | Data: {'command': 'generate_code', 'description': '创建一个计算斐波那契数列的函数'}
2025-07-31 21:28:23,863 - SelfCodingAgent - INFO - Processing command: generate_code
2025-07-31 21:28:23,863 - SelfCodingAgent - INFO - Generating code...
2025-07-31 21:28:23,863 - SelfCodingAgent - INFO - Generating code for: 创建一个计算斐波那契数列的函数
2025-07-31 21:28:23,864 - SelfCodingAgent - INFO - Code generated for: 创建一个计算斐波那契数列的函数 | Data: {'code_length': 594}
2025-07-31 21:28:23,864 - SelfCodingAgent - INFO - Code generated successfully: 创建一个计算斐波那契数列的函数_1753968503.py
2025-07-31 21:28:23,864 - SelfCodingAgent - INFO - Command file processed and moved to: commands/processed/1753968503_example_command.json
2025-07-31 21:28:23,865 - SelfCodingAgent - INFO - Agent status: RUNNING
2025-07-31 21:28:23,865 - SelfCodingAgent - INFO - Agent started successfully. Monitoring: commands
2025-07-31 21:28:23,865 - SelfCodingAgent - INFO - Place JSON command files in the commands directory to interact with the agent
2025-07-31 21:28:28,870 - SelfCodingAgent - INFO - Test completed | Data: {'commands_processed': 1, 'code_generated': 1, 'successful_executions': 0, 'failed_executions': 0, 'start_time': 1753968503.8650858, 'uptime': 5.005590200424194}
2025-07-31 21:28:28,871 - SelfCodingAgent - INFO - Agent status: STOPPING
2025-07-31 21:28:28,872 - SelfCodingAgent - INFO - Stopping command receiver
2025-07-31 21:28:28,873 - SelfCodingAgent - INFO - Agent status: STOPPED
2025-07-31 21:28:28,874 - SelfCodingAgent - INFO - Agent stopped successfully
2025-07-31 21:28:28,874 - SelfCodingAgent - INFO - Agent shutdown complete
2025-07-31 21:29:47,491 - SelfCodingAgent - INFO - Self-coding agent initialized
2025-07-31 21:29:47,492 - SelfCodingAgent - INFO - Starting agent in interactive mode
2025-07-31 21:29:47,493 - SelfCodingAgent - INFO - Press Ctrl+C to stop the agent
2025-07-31 21:29:47,493 - SelfCodingAgent - INFO - Agent status: STARTING
2025-07-31 21:29:47,493 - SelfCodingAgent - INFO - Starting code execution
2025-07-31 21:29:47,509 - SelfCodingAgent - INFO - Code executed successfully | Data: {'output': 'Hello, World!\n2 + 2 = 4\n'}
2025-07-31 21:29:47,510 - SelfCodingAgent - INFO - Execution environment test passed
2025-07-31 21:29:47,510 - SelfCodingAgent - INFO - Example command file created: commands/example_command.json
2025-07-31 21:29:47,511 - SelfCodingAgent - INFO - Starting command receiver, monitoring: commands
2025-07-31 21:29:47,512 - SelfCodingAgent - DEBUG - Processing command file: commands/example_command.json
2025-07-31 21:29:47,513 - SelfCodingAgent - INFO - Command received | Data: {'command': 'generate_code', 'description': '创建一个计算斐波那契数列的函数'}
2025-07-31 21:29:47,513 - SelfCodingAgent - INFO - Processing command: generate_code
2025-07-31 21:29:47,514 - SelfCodingAgent - INFO - Generating code...
2025-07-31 21:29:47,514 - SelfCodingAgent - INFO - Generating code for: 创建一个计算斐波那契数列的函数
2025-07-31 21:29:47,514 - SelfCodingAgent - INFO - Code generated for: 创建一个计算斐波那契数列的函数 | Data: {'code_length': 594}
2025-07-31 21:29:47,515 - SelfCodingAgent - INFO - Code generated successfully: 创建一个计算斐波那契数列的函数_1753968587.py
2025-07-31 21:29:47,515 - SelfCodingAgent - INFO - Command file processed and moved to: commands/processed/1753968587_example_command.json
2025-07-31 21:29:47,515 - SelfCodingAgent - INFO - Agent status: RUNNING
2025-07-31 21:29:47,516 - SelfCodingAgent - INFO - Agent started successfully. Monitoring: commands
2025-07-31 21:29:47,516 - SelfCodingAgent - INFO - Place JSON command files in the commands directory to interact with the agent
2025-07-31 21:33:33,697 - SelfCodingAgent - INFO - Received signal 2, shutting down...
2025-07-31 21:33:33,699 - SelfCodingAgent - INFO - Agent status: STOPPING
2025-07-31 21:33:33,700 - SelfCodingAgent - INFO - Stopping command receiver
2025-07-31 21:33:33,701 - SelfCodingAgent - INFO - Agent status: STOPPED
2025-07-31 21:33:33,702 - SelfCodingAgent - INFO - Agent stopped successfully
2025-07-31 21:33:33,702 - SelfCodingAgent - INFO - Agent shutdown complete
